from datetime import datetime, timed<PERSON>ta
from typing import Dict
from typing import Dict, List
from abc import ABC, abstractmethod
from typing import List, Dict, Any
import pandas as pd
import numpy as np


class Alpha(ABC):
    """抽象策略基类"""

    def __init__(self):
        super().__init__()

    @property
    @abstractmethod
    def required_fields(self) -> List[str]:
        """必须字段列表(子类实现)"""
        pass

    @property
    @abstractmethod
    def kline_period(self) -> int:
        """需要的历史K线窗口大小"""
        pass

    def execute(self, data: pd.DataFrame) -> Dict[str, float]:
        """
        对外统一入口(自动校验输入)

        data:K线面板数据(包括当前时间)

        """
        # 校验属性合法性
        self._validate_properties()
        # 校验输入数据合法性
        self._validate_input(data)
        # 调用子类实现的逻辑
        return self.on_data(data)

    @abstractmethod
    def on_data(self, data: pd.DataFrame) -> Dict[str, float]:
        """
        子类需实现的策略逻辑
        返回信号字典
        例如{signal: 1.0}
        """
        pass

    def _validate_properties(self) -> None:
        """校验子类属性合法性"""
        if (
            not isinstance(self.required_fields, list) or
            len(self.required_fields) == 0 or
            not all(isinstance(f, str) for f in self.required_fields)
        ):
            raise AttributeError(
                f"required_fields 必须是非空字符串列表,当前值: {self.required_fields}"
            )

        if not isinstance(self.kline_period, int) or self.kline_period <= 0:
            raise AttributeError(
                f"kline_period 必须是正整数,当前值: {self.kline_period}"
            )

    def _validate_input(self, data: pd.DataFrame) -> None:
        """校验输入数据合法性"""
        if not isinstance(data, pd.DataFrame):
            raise TypeError(f"需要DataFrame类型,实际收到: {type(data)}")

        if len(data) != self.kline_period:
            raise ValueError(
                f"K线数据窗口不符！需要 {self.kline_period} 根,实际 {len(data)} 根"
            )

        missing_fields = [
            f for f in self.required_fields if f not in data.columns]
        if missing_fields:
            raise ValueError(f"缺少必要字段: {missing_fields}")


class PositionCalculator(ABC):
    """仓位计算器基类（时间平仓抽象方法）"""

    def execute(self,
                current_factor: float,
                history_positions: pd.Series) -> float:
        """
        统一入口方法
        参数:
            current_factor: 当前时刻因子值
            history_positions: 含时间戳的仓位历史序列(带时间戳索引)
        """
        # 输入校验
        self._validate_input(current_factor, history_positions)

        # 调用子类仓位计算
        raw_position = self.compute_position(current_factor, history_positions)

        return float(np.clip(raw_position, 0.0, 1.0))

    @abstractmethod
    def compute_position(self,
                         factor: float,
                         history_pos: pd.Series) -> float:
        """子类必须实现的仓位计算逻辑"""
        pass

    def _validate_input(self,
                        factor: float,
                        positions: pd.Series) -> None:
        """输入校验"""
        if not isinstance(factor, (float, int)):
            raise TypeError("current_factor必须是数值类型")

        if not isinstance(positions, pd.Series):
            raise TypeError("仓位历史必须是pd.Series类型")

        if not isinstance(positions.index, pd.DatetimeIndex):
            raise ValueError("仓位序列需要时间戳索引")


class FixedExpiryPositionCalculator(PositionCalculator):
    """固定K线数平仓的仓位计算器"""

    def __init__(self, kline_interval: str = '1h'):
        """
        :param kline_interval: K线周期字符串,如'1h'(1小时)、'15m'(15分钟)
        """
        super().__init__()
        self.kline_duration = pd.to_timedelta(kline_interval)
        self.timestamps_arr = []
        self.signal_history = {}

    def compute_position(self,
                         factor: float,
                         history_pos: pd.Series) -> float:
        """带时间平仓逻辑的仓位计算"""
        # 如果有新信号则优先响应(把信号值作为仓位)
        last_entry_time = history_pos.index[-1]

        if factor != 0:
            open_signal = factor
            self.timestamps_arr.append(last_entry_time)
            return factor

        # 没有新信号时检查持仓有效期
        if not history_pos.empty:
            last_entry_time = history_pos.index[-1]

            # 推算当前时间（最后仓位时间 + 1个K线周期）
            current_time = last_entry_time + self.kline_duration
            expiry_threshold = 10 * self.kline_duration

            # 检查是否在有效期内
            if current_time-expiry_threshold >= self.timestamps_arr[0]:
                reduce_time = self.signal_history.pop(0)
                reduce_signal = -self.signal_history.pop(reduce_time)

        # 默认返回空仓
        return history_pos.iloc[-1]+open_signal+reduce_signal
