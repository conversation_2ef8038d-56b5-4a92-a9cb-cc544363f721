import pandas as pd
import lightgbm as lgb
from sklearn.discriminant_analysis import StandardScaler
from sklearn.model_selection import TimeSeriesSplit
import numpy as np
from sqlalchemy import create_engine
import inspect
from factors_lib.chao_factors_lib import ChaoFactorLib
from lightgbm import log_evaluation, early_stopping
MYSQL_CONFIG = {
    "host": "************",
    "user": "root",
    "password": "1234",
    "database": "ctadata"
}


class DataManager():

    def __init__(self):
        # self.__tim__ = ticker.TickerInfoManager()

        self.problem_ticker = []
        self.engine = create_engine(f"mysql+pymysql://{MYSQL_CONFIG['user']}:{
                                    MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}/{MYSQL_CONFIG['database']}")
        return

    def concat_data(self, data):
        """读取并合并单品类永续合约的K线数据"""
        query = f"SELECT * FROM {data.contract}"
        df = pd.read_sql(query, self.engine)
        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
        df.set_index('open_time', inplace=True)
        # 将所有K线数据合并
        data.data = df
        return


class Data():

    def __init__(self, contract):
        self.contract = contract
        return


currency = 'BTCUSDT_15m_2020_2025'
d = Data(currency)
dm = DataManager()
dm.concat_data(d)
df = d.data.copy()

df['target'] = df['close'].shift(-150)/df['close']-1
# 遍历 ChaoFactorLib 中所有以 "c_chu" 开头的函数
for name, func in inspect.getmembers(ChaoFactorLib, inspect.isfunction):
    if name.startswith("c_chu"):  # 只选择 "c_chuXXX" 相关的因子
        df[name] = func(df)

# 假设目标变量为 'target'，特征为 c_chu001 到 c_chu031
features = [f'c_chu{str(i).zfill(3)}' for i in range(1, 32)]

scaler = StandardScaler()
df[features] = scaler.fit_transform(df[features])  # 只对因子列进行标准化

df = df.dropna()
X = df[features]
y = df['target']

# 时间序列交叉验证
n_splits = 7
tscv = TimeSeriesSplit(n_splits=n_splits)

# 定义模型参数，参考第三名做法
params = {
    'objective': 'regression',
    'metric': 'rmse',
    'boosting_type': 'gbdt',
    'num_leaves': 63,
    'learning_rate': 0.002,
    'feature_fraction': 0.8,
    'bagging_fraction': 0.8,
    'bagging_freq': 3,
    'verbose': -1,
    'random_state': 42
}

oof = np.zeros(len(X))
feature_importance = pd.DataFrame(index=features)

for fold, (train_index, test_index) in enumerate(tscv.split(X)):
    X_train, X_test = X.iloc[train_index], X.iloc[test_index]
    y_train, y_test = y.iloc[train_index], y.iloc[test_index]

    train_data = lgb.Dataset(X_train, label=y_train)
    valid_data = lgb.Dataset(X_test, label=y_test)

    callbacks = [log_evaluation(period=100),
                 early_stopping(stopping_rounds=100)]
    model = lgb.train(
        params,
        train_data,
        num_boost_round=10000,
        valid_sets=[valid_data],
        # early_stopping_rounds=100,
        # verbose_eval=0,
        callbacks=callbacks,
    )

    oof[test_index] = model.predict(X_test)
    feature_importance[f'fold_{fold}'] = model.feature_importance()

# 计算特征重要性的平均值
feature_importance['mean'] = feature_importance.mean(axis=1)

# 计算相关性得分（这里简单用 np.corrcoef 计算）
corr_score = np.corrcoef(y, oof)[0, 1]
print(f"相关性得分: {corr_score}")

# 保存模型
# model.save_model('single_crypto_lgbm_model.txt')
