from setuptools import setup, Extension
from Cython.Build import cythonize
import os

# 设置 Linux 交叉编译工具
try:
    os.environ["CC"] = "x86_64-linux-gnu-gcc"
    os.environ["CXX"] = "x86_64-linux-gnu-g++"
    os.environ["LDSHARED"] = "x86_64-linux-gnu-gcc -shared"
    # 合并 CFLAGS 和 extra_compile_args
    os.environ["CFLAGS"] = "-fPIC -shared"
except Exception as e:
    print(f"设置环境变量时出错: {e}")

setup(
    name="final_factors",
    ext_modules=cythonize([
        Extension(
            "final_factors",
            # 假设文件是 .pyx 格式
            sources=["final_factors.py"],
            extra_compile_args=[],  # 由于已经在 CFLAGS 中设置，这里可以为空
            extra_link_args=["-shared"],
        )
    ]),
    zip_safe=False,
)
