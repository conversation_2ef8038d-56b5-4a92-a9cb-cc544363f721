from abc import ABC, abstractmethod
from typing import List, Dict, Any
import pandas as pd


class Alpha(ABC):
    """抽象策略基类（支持交易信号列表输出）"""

    def __init__(self):
        super().__init__()

    @property
    @abstractmethod
    def required_fields(self) -> List[str]:
        """必须字段列表（子类实现）"""
        pass

    @property
    @abstractmethod
    def kline_period(self) -> int:
        """需要的历史K线数量(子类实现)"""
        pass

    def execute(self,
                data: pd.DataFrame,
                position_history: pd.Series) -> List[Dict[str, Any]]:
        """
        对外统一入口（自动校验输入）
        参数:
            position_history: 历史仓位序列(pd.Series)
        返回:
            交易信号字典列表
        """
        # 校验属性合法性
        self._validate_properties()
        # 校验输入数据合法性
        self._validate_input(data, position_history)
        # 调用子类实现的逻辑
        return self.on_data(data, position_history)

    @abstractmethod
    def on_data(self,
                data: pd.DataFrame,
                position_history: pd.Series) -> List[Dict[str, Any]]:
        """
        子类需实现的策略逻辑
        返回格式示例:
        [{
            'signal': 'BUY/SELL', 
            'side': 'LONG/SHORT',
            'trade_volume': 0.5
        }]
        """
        pass

    @abstractmethod
    def get_params(self) -> Dict[str, Any]:
        """获取策略参数（保持原有功能）"""
        pass

    def _validate_properties(self) -> None:
        """校验子类属性合法性"""
        if (
            not isinstance(self.required_fields, list) or
            len(self.required_fields) == 0 or
            not all(isinstance(f, str) for f in self.required_fields)
        ):
            raise AttributeError(
                f"required_fields 必须是非空字符串列表，当前值: {self.required_fields}"
            )

        if not isinstance(self.kline_period, int) or self.kline_period <= 0:
            raise AttributeError(
                f"kline_period 必须是正整数，当前值: {self.kline_period}"
            )

    def _validate_input(self,
                        data: pd.DataFrame,
                        position_history: pd.Series) -> None:
        """校验输入数据合法性"""
        if not isinstance(data, pd.DataFrame):
            raise TypeError(f"需要DataFrame类型，实际收到: {type(data)}")

        if len(data) != self.kline_period:
            raise ValueError(
                f"K线数据长度不符！需要 {self.kline_period} 根，实际 {len(data)} 根"
            )

        missing_fields = [
            f for f in self.required_fields if f not in data.columns]
        if missing_fields:
            raise ValueError(f"缺少必要字段: {missing_fields}")

        if not isinstance(position_history, pd.Series):
            raise TypeError("仓位历史必须是pd.Series类型")

        if not all(0 <= v <= 1 for v in position_history):
            raise ValueError("仓位历史值必须在0~1之间")


class MovingAverageStrategy(Alpha):
    """均线策略（返回交易信号列表）"""

    def __init__(self, lookback: int = 30, ma_window: int = 20):
        super().__init__()
        self._required_fields = ['close']
        self._kline_period = lookback
        self.ma_window = ma_window

    @property
    def required_fields(self) -> List[str]:
        return self._required_fields.copy()

    @property
    def kline_period(self) -> int:
        return self._kline_period

    def on_data(self,
                data: pd.DataFrame,
                position_history: pd.Series) -> List[Dict[str, Any]]:
        """生成交易信号列表"""
        closes = data['close']
        current_price = closes.iloc[-1]
        ma = closes.rolling(self.ma_window).mean().iloc[-1]

        # 获取最新仓位状态
        current_position = position_history.iloc[-1] if len(
            position_history) > 0 else 0.0

        signals = []

        # 生成买入信号
        if current_price > ma and current_position <= 0.01:
            signals.append({
                'signal': 1,
                'side': 'LONG',
                'trade_volume': 1.0,
                'description': f'价格{current_price:.2f}上穿{ma:.2f}均线'
            })

        # 生成卖出信号
        elif current_price < ma and current_position > 0.01:
            signals.append({
                'signal': -1,
                'side': 'SHORT',
                'trade_volume': current_position,
                'description': f'价格{current_price:.2f}下穿{ma:.2f}均线'
            })

        return signals

    def get_params(self) -> Dict[str, Any]:
        """获取策略参数"""
        return {
            'kline_period': self.kline_period,
            'required_fields': self.required_fields,
            'ma_window': self.ma_window
        }


if __name__ == '__main__':
    # 初始化策略
    strategy = MovingAverageStrategy(lookback=30, ma_window=20)

    # 准备测试数据 ----------------------------------------------------
    # 生成价格序列：前25根K线低于均线，后5根突破均线
    base_price = 100.0
    test_prices = [base_price + i*0.5 for i in range(25)]  # 缓慢上涨
    test_prices += [base_price + 25 * 0.5 + i*3.0 for i in range(5)]  # 快速拉升

    test_data = pd.DataFrame({
        'close': test_prices
    })

    # 构建仓位历史（全周期空仓）
    position_hist = pd.Series(
        [0.0] * 30,  # 全部空仓
        index=pd.date_range('2023-01-01', periods=30)
    )

    # 执行策略 --------------------------------------------------------
    try:
        signals = strategy.execute(test_data, position_hist)
        print("生成交易信号:")
        for i, sig in enumerate(signals, 1):
            print(f"信号{i}: {sig}")

        print("\n策略参数:", strategy.get_params())

    except Exception as e:
        print(f"执行异常: {str(e)}")
