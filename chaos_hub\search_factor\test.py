from gplearn_factor_extraction import FactorMining
from sqlalchemy import create_engine
from math_operators import MathOperators
import pandas as pd
import numpy as np
from time import time
from data_service import DataService
from gplearn.functions import make_function
# 定义 SMA 函数（需要 2 个参数：价格序列和窗口大小）


def sma_func(prices):
    return (pd.Series(prices).rolling(5).mean().fillna(0)).values


def temp_func(s1, s2):
    s1 = pd.Series(s1)
    s2 = pd.Series(s2)
    bins = pd.qcut(s1, q=10, labels=False, duplicates='drop')

    grouped = s2.groupby(bins)
    group_means = grouped.mean()

    s2_centered = s2.copy()
    for group_num, mean_value in group_means.items():
        mask = (bins == group_num)  # 找到属于当前组的索引
        s2_centered[mask] = s2[mask] - mean_value  # 减去组内均值
    return s2_centered.values


def box_cox_func(x):
    if np.any(x <= 0):
        return pd.Series(np.nan * np.ones_like(x)).fillna(0).values
    return pd.Series((np.power(x, 2) - 1) / 2).fillna(0).values


# 使用 make_function 创建自定义算子
sma_operator = make_function(
    function=sma_func,
    name='SMA',  # 算子名称
    arity=1      # 需要 2 个参数
)

temp_operator = make_function(
    function=temp_func,
    name='TEMP',
    arity=2
)

box_cox_operator = make_function(
    function=box_cox_func,
    name='BOX_COX',
    arity=1
)


if __name__ == "__main__":
    # ds = DataService()
    # df = ds['ETHUSDT_15m_2020_2025']['2021-10-01':]

    # c_chu_list = []
    # for i in range(1, 53):  # 1 到 52
    #     item = f"c_chu{i:03d}"  # 格式化数字为 3 位，不足补 0
    #     c_chu_list.append(item)

    # for c in c_chu_list:
    #     df[c] = ds['ETHUSDT_15m_2020_2025', c]['2021-10-01':]
    #     print(c, '已完成')

    # # 把df存成pickle
    # df.to_pickle('df_data.pickle')
    # exit()

    # 初始化并运行因子挖掘
    df = pd.read_pickle('df_data.pickle')
    features = [x for x in df.columns.to_list() if 'chu' in x]
    # features = list(df.columns)
    target_column = "close"

    start_time = time()
    factor_miner = FactorMining(
        data=df,
        features=features,

        target_column=target_column,
        target_period=10,                            # 预测 target_period 期后的收益
        population_size=500000,                       # 种群规模
        generations=10000,                             # 进化代数
        function_set=('add', 'sub', 'mul', 'div',
                      'sqrt', 'log', 'abs', 'neg', 'inv', 'sin', 'cos', 'tan', 'sigmoid', sma_operator, temp_operator, box_cox_operator),        # 表达式
        test_size=0.6,
        random_state=42,
        metric='spearman'
    )

    best_expression, rank_ic, ic = factor_miner.run()
    end_time = time()

    print("\n========= 运行结果 =========")
    print("最佳因子表达式:", best_expression)
    print("IC (Pearson):", ic)
    print("Rank IC (Spearman):", rank_ic)
    print(f"运行时间: {end_time - start_time:.2f} 秒")
