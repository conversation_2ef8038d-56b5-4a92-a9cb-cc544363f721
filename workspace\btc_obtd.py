import csv
import datetime
import time
from decimal import Decimal
import numpy as np
import pandas as pd
import ccxt
import requests
import json
import pymysql
from collections import deque


def hf_connection():
    return pymysql.connect(host="*************",
                           port=3306,
                           user="root",
                           password="Hf2024888*",
                           database="obtc",
                           )


def jf_mysql_connection():
    connection = pymysql.connect(host="**************",
                                 port=3306,
                                 user="root",
                                 password="Kk11cc00..",
                                 database="obtc",
                                 )
    return connection


with open('apis.json', 'r') as api_file:
    apis = json.load(api_file)

# binance
binance_key = apis.get('binance_key')
binance_secret = apis.get('binance_secret')
binance_swap = ccxt.binance({'apiKey': binance_key, 'secret': binance_secret, "options": {"defaultType": "future"}})
binance_spot = ccxt.binance({'apiKey': binance_key, 'secret': binance_secret})

# coinbase
coinbase_key = apis.get('coinbase_key')
coinbase_secret = apis.get('coinbase_secret')
coinbase = ccxt.coinbase({'api_key': coinbase_key, 'secret_key': coinbase_secret, 'password': "Kk11cc00..",
                          'verbose': False})
# okx
okx_key = apis.get('okx_key')
okx_secret = apis.get('okx_secret')
okx = ccxt.okx({'apiKey': okx_key, 'secret': okx_secret, 'password': 'Light299792458..', "options": {"defaultType": "spot"}})

# bybit
bybit_key = apis.get('bybit_key')
bybit_secret = apis.get('bybit_secret')
bybit = ccxt.bybit({'apiKey': bybit_key, 'secret': bybit_secret, "options": {"defaultType": "spot"}})

# gate
gate_key = apis.get('gate_key')
gate_secret = apis.get('gate_secret')
gate_spot = ccxt.gateio({'apiKey': gate_key, 'secret': gate_secret, "options": {"defaultType": "spot"}})
gate_swap = ccxt.gateio({'apiKey': gate_key, 'secret': gate_secret, "options": {"defaultType": "swap"}})

# kraken
kraken_key = apis.get('kraken_key')
kraken_secret = apis.get('kraken_secret')
kraken = ccxt.kraken({'apiKey': kraken_key, 'secret': kraken_secret, "options": {"defaultType": "spot"}})

ding_webhook = "https://oapi.dingtalk.com/robot/send?access_token=cb13e536fed02bf80bf697550d1148901bbd2dbfe7244bfd29c8b22999b2e4d4"  # 交易信号
feishu_webhook = "https://open.feishu.cn/open-apis/bot/v2/hook/0ef3e56c-13d5-4f3b-ac3d-04f8f207dcef"  # 交易信号
lark_webhook = "https://open.larksuite.com/open-apis/bot/v2/hook/af246f30-797b-4fda-90be-584fefb844b0"  # Trade Signal

ok_symbol = 'BTC/USDT:USDT'
gt_symbol = 'BTC/USDT:USDT'
bn_swap_symbol = 'BTCUSDT'
bn_future_symbol = 'BTCUSD_PERP'
ok_size = 1
gt_size = 1
bn_swap_size = 0.002
last_signal = None
leve = 100
fee = 0.0005
maxlength = 60

order_imbalance_deque = deque(maxlen=maxlength)
liquidity_imbalance_deque = deque(maxlen=maxlength)
depth_ratio_deque = deque(maxlen=maxlength)


def ex_name(exchange):
    if exchange == gate_swap:
        exchange_name = 'gate'
    elif exchange == binance_swap:
        exchange_name = 'bn'
    else:
        exchange_name = '_'
    return exchange_name

#
# def send_dingtalk_message(webhook_url, message):
#     data = {"msgtype": "text",
#             "text": {"content": message}
#             }
#     headers = {"Content-Type": "application/json;charset=utf-8"}
#     response = requests.post(webhook_url, data=json.dumps(data), headers=headers)
#     return response.json()
#
#
# def send_telegram(text):
#     token = '**********************************************'
#     chat_id = '-1002162187628'
#     url = 'https://api.telegram.org/bot{}/sendMessage'.format(token)
#     headers = {'Content-Type': 'application/json'}
#     data = {'chat_id': '{}'.format(chat_id),
#             'text': text
#             }
#     response = requests.post(url=url, headers=headers, json=data)
#     return response
#
#
# def send_lark_message(webhook_url, message):
#     data = {"msg_type": "text",
#             "content": {"text": message}
#             }
#     headers = {"Content-Type": "application/json; charset=utf-8"}
#     response = requests.post(url=webhook_url, data=json.dumps(data), headers=headers)
#     return response.json()
#

# def send_message(exchange, message_cn, message_en):
#     if exchange == bn_qiu:
#         send_dingtalk_message(ding_webhook, message_cn)
#         send_lark_message(lark_webhook, message_en)
#         send_telegram(message_en)
#     if exchange == bn_mia:
#         send_lark_message(feishu_webhook, message_cn)


def insert_trades_to_mysql(data):
    conn = hf_connection()
    cursor = conn.cursor()
    insert_sql = """INSERT INTO hf_btc_trades (
    account_id, 
    order_symbol, 
    order_side, 
    order_volume, 
    open_time, 
    open_price, 
    take_profit_price, 
    stop_loss_price, 
    order_status, 
    close_time, 
    close_price, 
    profit_or_loss) 
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"""

    try:
        cursor.execute(insert_sql, data)
        conn.commit()
    except Exception as e:
        print(e)
        conn.rollback()
    finally:
        cursor.close()
        conn.close()


def insert_profits_to_mysql(data):
    conn = hf_connection()
    cursor = conn.cursor()
    insert_sql = """
    INSERT INTO hf_btc_profits (
    date, 
    account_id,
    total_balance, 
    profit_today,
    annul_profit_today, 
    annul_profit_total)
    VALUES (%s, %s, %s, %s, %s, %s)
    """

    try:
        cursor.execute(insert_sql, data)
        conn.commit()
    except Exception as e:
        print(e)
        conn.rollback()
    finally:
        cursor.close()
        conn.close()


def insert_signal_to_mysql(data):
    conn = hf_connection()
    cursor = conn.cursor()
    insert_sql = """
    INSERT INTO market_data (
    datetime,
    bid,
    ask,
    buy,
    sell,
    price,
    trade_signal)
    VALUES (%s, %s, %s, %s, %s, %s, %s)
    """
    try:
        cursor.execute(insert_sql, data)
        conn.commit()
    except Exception as e:
        print(e)
        conn.rollback()
    finally:
        cursor.close()
        conn.close()


# def insert_into_jf_obtc(data):
#     hf_connection() = pymysql.connect(host="**************",
#                                  port=3306,
#                                  user="root",
#                                  password="Kk11cc00..",
#                                  database="obtc"
#                                  )
#     cursor = hf_connection().cursor()
#
#     insert_sql = """
#     INSERT INTO orderbook_tradecount (
#     datetime,
#     bid,
#     ask,
#     buy,
#     sell,
#     price)
#     VALUES (%s, %s, %s, %s, %s, %s)
#     """
#
#     try:
#         cursor.execute(insert_sql, data)
#         hf_connection().commit()
#     except Exception as e:
#         print(e)
#         hf_connection().rollback()
#     finally:
#         cursor.close()
#         hf_connection().close()


def read_lines_from_mysql(order_status):
    conn = hf_connection()
    cursor = conn.cursor()
    select_query = f"""
    SELECT * FROM hf_btc_trades 
    WHERE order_status = '{order_status}'
    """
    try:
        cursor.execute(select_query)
        results = cursor.fetchall()
        return results
    except pymysql.err.OperationalError as err:
        return {"error": str(err)}
    finally:
        cursor.close()
        conn.close()


def update_mysql(column, value, open_time):
    conn = hf_connection()
    cursor = conn.cursor()
    update_query = f"""
    UPDATE hf_btc_trades 
    SET {column} = %s 
    WHERE open_time = %s
    """
    try:
        cursor.execute(update_query, (value, open_time))
        conn.commit()
    except pymysql.err.OperationalError as err:
        print("error", str(err))
    finally:
        cursor.close()
        conn.close()


def swap_ob(exchange, symbol):
    ob = binance_swap.fetch_order_book(symbol=symbol, limit=10)
    bid1_price = ob['bids'][0][0]
    ask1_price = ob['asks'][0][0]
    return bid1_price, ask1_price


def posi(exchange, symbol):
    long_posi = 0
    short_posi = 0
    long_unrppct = 0
    short_unrppct = 0
    contractSize = 0
    long_entryprice = 0
    short_entryprice = 0
    positions = exchange.fetch_positions(symbols=[symbol])
    if len(positions) > 0:
        contractSize = positions[0]['contractSize']
        for i in positions:
            if i['side'] == 'long':
                long_posi = i['contracts']
                long_entryprice = i['entryPrice']
                if i['collateral'] != 0:
                    long_unrppct = i['unrealizedPnl'] / i['collateral']
            if i['side'] == 'short':
                short_posi = i['contracts']
                short_entryprice = i['entryPrice']
                if i['collateral'] != 0:
                    short_unrppct = i['unrealizedPnl'] / i['collateral']
    return long_posi, short_posi, long_unrppct, short_unrppct, contractSize, long_entryprice, short_entryprice


def fetch_open_orders(exchange, symbol):
    open_long_num = []
    open_short_num = []
    close_short_num = []
    close_long_num = []
    stop_long_id = None
    stop_long_num = 0
    stop_short_id = None
    stop_short_num = 0
    open_orders = exchange.fetch_open_orders(symbol=symbol)
    if len(open_orders) > 0:
        for i in open_orders:
            if i['reduceOnly'] is False and i['side'] == 'buy':
                open_long_num.append(i['amount'])
            if i['reduceOnly'] is False and i['side'] == 'sell':
                open_short_num.append(i['amount'])
            if i['reduceOnly'] is True and i['side'] == 'buy':
                close_short_num.append(i['amount'])
            if i['reduceOnly'] is True and i['side'] == 'sell':
                close_long_num.append(i['amount'])
            if i['type'] == 'stop_market' and i['side'] == 'sell':
                stop_long_id = i['id']
                stop_long_num = i['amount']
            if i['type'] == 'stop_market' and i['side'] == 'buy':
                stop_short_id = i['id']
                stop_short_num = i['amount']
    return (sum(open_long_num), sum(open_short_num), sum(close_long_num), sum(close_short_num),
            stop_long_id, stop_long_num, stop_short_id, stop_short_num)


def close_params(exchange, symbol, amount):
    close_long_amount, close_short_amount = amount, amount
    close_long_params, close_short_params = {}, {}
    long_posi, short_posi, long_unrppct, short_unrppct, contractSize, long_entryprice, short_entryprice = posi(
        exchange, symbol)
    if str(exchange) == 'Binance':
        close_long_params = {'positionSide': 'LONG'}
        close_short_params = {'positionSide': 'SHORT'}
        if last_signal == 'buy' and short_posi >= 3 * amount and short_unrppct > 2 * fee * leve:
            close_short_amount = round(short_posi / 3, 3)
        if last_signal == 'sell' and long_posi >= 3 * amount and long_unrppct > 2 * fee * leve:
            close_long_amount = round(long_posi / 3, 3)
    if str(exchange) == 'Gate.io':
        close_long_params = {'positionSide': 'LONG', 'timeInForce': 'gtc', 'reduceOnly': True}
        close_short_params = {'positionSide': 'SHORT', 'timeInForce': 'gtc', 'reduceOnly': True}
        if last_signal == 'buy' and short_posi >= 3 * amount and short_unrppct > 2 * fee * leve:
            close_short_amount = round(short_posi / 3 * contractSize, 4) / contractSize
        if last_signal == 'sell' and long_posi >= 3 * amount and long_unrppct > 2 * fee * leve:
            close_long_amount = round(long_posi / 3 * contractSize, 4) / contractSize
    if str(exchange) == 'OKX':
        close_long_params = {'posSide': 'long', 'tdMode': 'isolated'}
        close_short_params = {'posSide': 'short', 'tdMode': 'isolated'}
        if last_signal == 'buy' and short_posi >= 3 * amount and short_unrppct > 2 * fee * leve:
            close_short_amount = round(short_posi / 3 * contractSize, 2) / contractSize
        if last_signal == 'sell' and long_posi >= 3 * amount and long_unrppct > 2 * fee * leve:
            close_long_amount = round(long_posi / 3 * contractSize, 2) / contractSize
    return close_long_amount, close_short_amount, close_long_params, close_short_params


def open_long(exchange, amount, symbol):
    ask1 = swap_ob(exchange, symbol)[1]
    r = exchange.create_order(symbol=symbol,
                              amount=amount,
                              type='limit',
                              price=ask1,
                              side='buy',
                              params={'positionSide': 'LONG'})
    return r


def open_short(exchange, amount, symbol):
    bid1 = swap_ob(exchange, symbol)[0]
    r = exchange.create_order(symbol=symbol,
                              amount=amount,
                              type='limit',
                              price=bid1,
                              side='sell',
                              params={'positionSide': 'SHORT'})
    return r


def close_long(exchange, amount, symbol):
    long_posi = posi(exchange, symbol)[0]
    close_long_num = fetch_open_orders(exchange, symbol)[2]
    bid1, ask1 = swap_ob(exchange, symbol)
    if long_posi - close_long_num >= amount:
        r = exchange.create_order(symbol=symbol,
                                  amount=amount,
                                  type='limit',
                                  price=bid1,
                                  side='sell',
                                  params=close_params(exchange, symbol, amount)[2])
        return r
    else:
        pass


def close_short(exchange, amount, symbol):
    short_posi = posi(exchange, symbol)[1]
    close_short_num = fetch_open_orders(exchange, symbol)[3]
    bid1, ask1 = swap_ob(exchange, symbol)
    if short_posi - close_short_num >= amount:
        r = exchange.create_order(symbol=symbol,
                                  amount=amount,
                                  type='limit',
                                  price=ask1,
                                  side='buy',
                                  params=close_params(exchange, symbol, amount)[3])
        return r
    else:
        pass


# ob_df = pd.DataFrame({'bn_usdt_bids': [0], 'bn_usdt_asks': [0],
#                       'bn_fdusd_bids': [0], 'bn_fdusd_asks': [0],
#                       'cb_bids': [0], 'cb_asks': [0],
#                       'ok_bids': [0], 'ok_asks': [0],
#                       'bb_bids': [0], 'bb_asks': [0],
#                       'gt_bids': [0], 'gt_asks': [0],
#                       'kk_bids': [0], 'kk_asks': [0],
#                       'mid_price': [0],
#                       },
#                      columns=['bn_usdt_bids', 'bn_usdt_asks',
#                               'bn_fdusd_bids', 'bn_fdusd_asks',
#                               'cb_bids', 'cb_asks',
#                               'ok_bids', 'ok_asks',
#                               'bb_bids', 'bb_asks',
#                               'gt_bids', 'gt_asks',
#                               'kk_bids', 'kk_asks',
#                               'mid_price',
#                               ]
#                      )


def insert_ob_to_jf_mysql(table, data):
    conn = jf_mysql_connection()
    cursor = conn.cursor()
    columns = ['datetime']
    values = [datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')]
    for i in range(10):
        try:
            bid = data['bids'][i][:2]
            ask = data['asks'][i][:2]
            if len(bid) != 2 or len(ask) != 2:
                raise ValueError("Invalid bid/ask entry")
            bid_p, bid_v = bid
            ask_p, ask_v = ask
        except Exception:
            bid_p = bid_v = ask_p = ask_v = None
        columns += [f'bid{i + 1}_p', f'bid{i + 1}_v', f'ask{i + 1}_v', f'ask{i + 1}_p']
        values += [bid_p, bid_v, ask_v, ask_p]
    placeholders = ', '.join(['%s'] * len(columns))
    column_names = ', '.join([f'`{col}`' for col in columns])
    sql = f"INSERT INTO {table} ({column_names}) VALUES ({placeholders})"
    cursor.execute(sql, values)
    conn.commit()
    cursor.close()


def order_book():
    bn_usdt_ob = binance_spot.fetch_order_book(symbol='BTCUSDT', limit=10)
    bn_fdusd_ob = binance_spot.fetch_order_book(symbol='BTCFDUSD', limit=10)
    cb_ob = coinbase.fetch_order_book(symbol='BTC/USD', limit=10)
    ok_ob = okx.fetch_order_book(symbol='BTC/USDT', limit=10)
    bb_ob = bybit.fetch_order_book(symbol='BTC/USDT', limit=10)
    gt_ob = gate_spot.fetch_order_book(symbol='BTC/USDT', limit=10)
    kk_ob = kraken.fetch_order_book(symbol='BTC/USD', limit=10)
    fx_ob = requests.get("https://openapi.fameex.com/sapi/v1/depth?symbol=BTCUSDT&limit=10",
                         headers={"Content-Type": "application/json"}).json()
    dx_ob = requests.get("https://openapi.darkex.com/sapi/v1/depth",
                         params={"symbol": "BTCUSDT", "limit": 10}).json()
    obs = [bn_usdt_ob, bn_fdusd_ob, cb_ob, ok_ob, bb_ob, gt_ob, kk_ob, fx_ob, dx_ob]
    tables = ['bn_usdt_ob', 'bn_fdusd_ob', 'cb_ob', 'ok_ob', 'bb_ob', 'gt_ob', 'kk_ob', 'fx_ob', 'dx_ob']
    # 把ob数据插入到mysql数据库
    for ob, table in zip(obs, tables):
        try:
            insert_ob_to_jf_mysql(table, ob)
        except pymysql.err.OperationalError as conn_err:
            print("jf_mysql连接异常，尝试重连:", conn_err)
            jf_mysql_connection()
        except Exception as other_err:
            print(other_err)
            jf_mysql_connection().rollback()

    # bids: 买方挂单列表，按价格从高到低排序，每个元素是[价格, 数量]
    # asks: 卖方挂单列表，按价格从低到高排序，每个元素是[价格, 数量]
    bn_usdt_bids = bn_usdt_ob['bids']  # 获取买盘数据
    bn_usdt_asks = bn_usdt_ob['asks']  # 获取卖盘数据

    # 将买盘和卖盘数据转换为DataFrame，便于后续分析处理
    # columns=['price', 'quantity']: 指定列名为价格和数量
    bn_usdt_bids_df = pd.DataFrame(bn_usdt_bids, columns=['price', 'quantity'])[0:10]  # 买盘DataFrame
    bn_usdt_asks_df = pd.DataFrame(bn_usdt_asks, columns=['price', 'quantity'])[0:10]  # 卖盘DataFrame

    # 计算中间价(mid price)：最优买价和最优卖价的平均值
    # iloc[0]: 取第一档(最优)价格
    bn_usdt_mid = (bn_usdt_bids_df['price'].iloc[0] + bn_usdt_asks_df['price'].iloc[0]) / 2  # 中间价

    # 计算买盘和卖盘前10档的总挂单量
    bn_usdt_bids_sum = sum(bn_usdt_bids_df['quantity'])  # 买盘总挂单量(BTC)
    bn_usdt_asks_sum = sum(bn_usdt_asks_df['quantity'])  # 卖盘总挂单量(BTC)

    # FDUSD
    bn_fdusd_bids = bn_fdusd_ob['bids']
    bn_fdusd_asks = bn_fdusd_ob['asks']
    bn_fdusd_bids_df = pd.DataFrame(bn_fdusd_bids, columns=['price', 'quantity'])[0:10]
    bn_fdusd_asks_df = pd.DataFrame(bn_fdusd_asks, columns=['price', 'quantity'])[0:10]
    bn_fdusd_mid = (bn_fdusd_bids_df['price'].iloc[0] + bn_fdusd_asks_df['price'].iloc[0]) / 2
    bn_fdusd_bids_sum = sum(bn_fdusd_bids_df['quantity'])
    bn_fdusd_asks_sum = sum(bn_fdusd_asks_df['quantity'])

    cb_bids = cb_ob['bids']
    cb_asks = cb_ob['asks']
    cb_bids_df = pd.DataFrame(cb_bids, columns=['price', 'quantity'])[0:10]
    cb_asks_df = pd.DataFrame(cb_asks, columns=['price', 'quantity'])[0:10]
    cb_mid = (cb_bids_df['price'].iloc[0] + cb_asks_df['price'].iloc[0]) / 2
    cb_bids_sum = sum(cb_bids_df['quantity'])
    cb_asks_sum = sum(cb_asks_df['quantity'])

    ok_bids = ok_ob['bids']
    ok_asks = ok_ob['asks']
    ok_bids_df = pd.DataFrame(ok_bids, columns=['price', 'quantity', 'num'])[0:10]
    ok_asks_df = pd.DataFrame(ok_asks, columns=['price', 'quantity', 'num'])[0:10]
    ok_mid = (ok_bids_df['price'].iloc[0] + ok_asks_df['price'].iloc[0]) / 2
    ok_bids_sum = sum(ok_bids_df['quantity'])
    ok_asks_sum = sum(ok_asks_df['quantity'])

    bb_bids = bb_ob['bids']
    bb_asks = bb_ob['asks']
    bb_bids_df = pd.DataFrame(bb_bids, columns=['price', 'quantity'])[0:10]
    bb_asks_df = pd.DataFrame(bb_asks, columns=['price', 'quantity'])[0:10]
    bb_mid = (bb_bids_df['price'].iloc[0] + bb_asks_df['price'].iloc[0]) / 2
    bb_bids_sum = sum(bb_bids_df['quantity'])
    bb_asks_sum = sum(bb_asks_df['quantity'])

    gt_bids = gt_ob['bids']
    gt_asks = gt_ob['asks']
    gt_bids_df = pd.DataFrame(gt_bids, columns=['price', 'quantity'])[0:10]
    gt_asks_df = pd.DataFrame(gt_asks, columns=['price', 'quantity'])[0:10]
    gt_mid = (gt_bids_df['price'].iloc[0] + gt_asks_df['price'].iloc[0]) / 2
    gt_bids_sum = sum(gt_bids_df['quantity'])
    gt_asks_sum = sum(gt_asks_df['quantity'])

    kk_bids = kk_ob['bids']
    kk_asks = kk_ob['asks']
    kk_bids_df = pd.DataFrame(kk_bids, columns=['price', 'quantity', 'ts'])[0:10]
    kk_asks_df = pd.DataFrame(kk_asks, columns=['price', 'quantity', 'ts'])[0:10]
    kk_mid = (kk_bids_df['price'].iloc[0] + kk_asks_df['price'].iloc[0]) / 2
    kk_bids_sum = sum(kk_bids_df['quantity'])
    kk_asks_sum = sum(kk_asks_df['quantity'])

    fx_bids = fx_ob['bids']
    fx_asks = fx_ob['asks']
    fx_bids_df = pd.DataFrame(fx_bids, columns=['price', 'quantity'])[0:10]
    fx_asks_df = pd.DataFrame(fx_asks, columns=['price', 'quantity'])[0:10]
    fx_mid = (fx_bids_df['price'].iloc[0] + fx_asks_df['price'].iloc[0]) / 2
    fx_bids_sum = sum(fx_bids_df['quantity'])
    fx_asks_sum = sum(fx_asks_df['quantity'])

    dx_bids = dx_ob['bids']
    dx_asks = dx_ob['asks']
    dx_bids_df = pd.DataFrame(dx_bids, columns=['price', 'quantity'])[0:10]
    dx_asks_df = pd.DataFrame(dx_asks, columns=['price', 'quantity'])[0:10]
    dx_mid = (dx_bids_df['price'].iloc[0] + dx_asks_df['price'].iloc[0]) / 2
    dx_bids_sum = sum(dx_bids_df['quantity'])
    dx_asks_sum = sum(dx_asks_df['quantity'])

    # 所有交易所的买量总和
    bid_sum = (bn_usdt_bids_sum + bn_fdusd_bids_sum + cb_bids_sum + ok_bids_sum + bb_bids_sum + gt_bids_sum
               + kk_bids_sum + fx_bids_sum + dx_bids_sum)
    # 所有交易所的卖量总和
    ask_sum = (bn_usdt_asks_sum + bn_fdusd_asks_sum + cb_asks_sum + ok_asks_sum + bb_asks_sum + gt_asks_sum
               + kk_asks_sum + fx_asks_sum + dx_asks_sum)
    # 所有交易所的中间价的mean
    mid_price = (bn_usdt_mid + bn_fdusd_mid + cb_mid + ok_mid + bb_mid + gt_mid + kk_mid + fx_mid + dx_mid) / 9
    return bid_sum, ask_sum, mid_price


def recent_trades():
    ts = int((time.time() - 10) * 1000)
    bn_usdt_trades = binance_spot.fetch_trades(symbol='BTCUSDT', since=ts)
    bn_fdusd_trades = binance_spot.fetch_trades(symbol='BTCFDUSD', since=ts)
    cb_trades = coinbase.fetch_trades(symbol='BTC/USD', since=ts, params={'until': time.time() * 1000})
    ok_trades = okx.fetch_trades(symbol='BTC/USDT', since=ts)
    bb_trades = bybit.fetch_trades(symbol='BTC/USDT', since=ts)
    gt_trades = gate_spot.fetch_trades(symbol='BTC/USDT', since=ts)
    kk_trades = kraken.fetch_trades(symbol='BTC/USD', since=ts)
    fx_trades = requests.get("https://openapi.fameex.com/sapi/v1/trades?symbol=BTCUSDT&limit=1000",
                             headers={"Content-Type": "application/json"}).json()
    dx_trades = requests.get("https://openapi.darkex.com/sapi/v1/trades",
                             params={"symbol": "BTCUSDT", "limit": 1000}).json()

    df0 = pd.DataFrame({'timestamp': [0], 'side': [0], 'amount': [0]})
    bn_trades_usdt_df = pd.DataFrame(bn_usdt_trades)[['timestamp', 'side', 'amount']] if len(
        bn_usdt_trades) > 0 else df0
    bn_trades_fdusd_df = pd.DataFrame(bn_fdusd_trades)[['timestamp', 'side', 'amount']] if len(
        bn_fdusd_trades) > 0 else df0
    cb_trades_df = pd.DataFrame(cb_trades)[['timestamp', 'side', 'amount']] if len(cb_trades) > 0 else df0
    ok_trades_df = pd.DataFrame(ok_trades)[['timestamp', 'side', 'amount']] if len(ok_trades) > 0 else df0
    bb_trades_df = pd.DataFrame(bb_trades)[['timestamp', 'side', 'amount']] if len(bb_trades) > 0 else df0
    gt_trades_df = pd.DataFrame(gt_trades)[['timestamp', 'side', 'amount']] if len(gt_trades) > 0 else df0
    kk_trades_df = pd.DataFrame(kk_trades)[['timestamp', 'side', 'amount']] if len(kk_trades) > 0 else df0

    fx_trades_df = pd.DataFrame(fx_trades)
    fx_trades_df.columns = ['side', 'price', 'amount', 'timestamp']
    fx_trades_df = fx_trades_df[['timestamp', 'side', 'amount']] if len(fx_trades) > 0 else df0
    fx_trades_df = fx_trades_df[fx_trades_df['timestamp'] > ts]

    dx_trades_df = pd.DataFrame(dx_trades['list'])
    dx_trades_df.columns = ['symbol', 'side', 'price', 'amount', 'id', 'timestamp']
    dx_trades_df = dx_trades_df[['timestamp', 'side', 'amount']] if len(dx_trades) > 0 else df0
    dx_trades_df = dx_trades_df[dx_trades_df['timestamp'] > ts]
    dx_trades_df['amount'] = dx_trades_df['amount'].astype(float)

    bn_usdt_buy = bn_trades_usdt_df[bn_trades_usdt_df['side'] == 'buy'][['timestamp', 'amount']]
    bn_usdt_sell = bn_trades_usdt_df[bn_trades_usdt_df['side'] == 'sell'][['timestamp', 'amount']]

    bn_fdusd_buy = bn_trades_fdusd_df[bn_trades_fdusd_df['side'] == 'buy'][['timestamp', 'amount']]
    bn_fdusd_sell = bn_trades_fdusd_df[bn_trades_fdusd_df['side'] == 'sell'][['timestamp', 'amount']]

    cb_buy = cb_trades_df[cb_trades_df['side'] == 'buy'][['timestamp', 'amount']]
    cb_sell = cb_trades_df[cb_trades_df['side'] == 'sell'][['timestamp', 'amount']]

    ok_buy = ok_trades_df[ok_trades_df['side'] == 'buy'][['timestamp', 'amount']]
    ok_sell = ok_trades_df[ok_trades_df['side'] == 'sell'][['timestamp', 'amount']]

    bb_buy = bb_trades_df[bb_trades_df['side'] == 'buy'][['timestamp', 'amount']]
    bb_sell = bb_trades_df[bb_trades_df['side'] == 'sell'][['timestamp', 'amount']]

    gt_buy = gt_trades_df[gt_trades_df['side'] == 'buy'][['timestamp', 'amount']]
    gt_sell = gt_trades_df[gt_trades_df['side'] == 'sell'][['timestamp', 'amount']]

    kk_buy = kk_trades_df[kk_trades_df['side'] == 'buy'][['timestamp', 'amount']]
    kk_sell = kk_trades_df[kk_trades_df['side'] == 'sell'][['timestamp', 'amount']]

    fx_buy = fx_trades_df[fx_trades_df['side'] == 'buy'][['timestamp', 'amount']]
    fx_sell = fx_trades_df[fx_trades_df['side'] == 'sell'][['timestamp', 'amount']]

    dx_buy = dx_trades_df[dx_trades_df['side'] == 'BUY'][['timestamp', 'amount']]
    dx_sell = dx_trades_df[dx_trades_df['side'] == 'SELL'][['timestamp', 'amount']]

    buy_merged = (
        bn_usdt_buy.merge(bn_fdusd_buy, on='timestamp', how='outer', suffixes=('_bn_usdt_buy', '_bn_fdusd_buy'))
        .merge(cb_buy, on='timestamp', how='outer', suffixes=('', '_cb_buy'))
        .merge(ok_buy, on='timestamp', how='outer', suffixes=('', '_ok_buy'))
        .merge(bb_buy, on='timestamp', how='outer', suffixes=('', '_bb_buy'))
        .merge(gt_buy, on='timestamp', how='outer', suffixes=('', '_gt_buy'))
        .merge(kk_buy, on='timestamp', how='outer', suffixes=('', '_kk_buy'))
        .merge(fx_buy, on='timestamp', how='outer', suffixes=('', '_fx_buy'))
        .merge(dx_buy, on='timestamp', how='outer', suffixes=('', '_dx_buy'))
    )
    buy_merged.fillna(0, inplace=True)
    buy_merged['buy_sum'] = buy_merged.iloc[:, 1:].sum(axis=1)

    sell_merged = (
        bn_usdt_sell.merge(bn_fdusd_sell, on='timestamp', how='outer', suffixes=('_bn_usdt_sell', '_bn_fdusd_sell'))
        .merge(cb_sell, on='timestamp', how='outer', suffixes=('', '_cb_sell'))
        .merge(ok_sell, on='timestamp', how='outer', suffixes=('', '_ok_sell'))
        .merge(bb_sell, on='timestamp', how='outer', suffixes=('', '_bb_sell'))
        .merge(gt_sell, on='timestamp', how='outer', suffixes=('', '_gt_sell'))
        .merge(kk_sell, on='timestamp', how='outer', suffixes=('', '_kk_sell'))
        .merge(fx_sell, on='timestamp', how='outer', suffixes=('', '_fx_sell'))
        .merge(dx_sell, on='timestamp', how='outer', suffixes=('', '_dx_sell'))
    )
    sell_merged.fillna(0, inplace=True)
    sell_merged['sell_sum'] = sell_merged.iloc[:, 1:].sum(axis=1)

    buy_merged['timestamp'] = buy_merged['timestamp'] // 1000
    sell_merged['timestamp'] = sell_merged['timestamp'] // 1000
    buy_merged['timestamp'] = pd.to_datetime(buy_merged['timestamp'], unit='s')
    sell_merged['timestamp'] = pd.to_datetime(sell_merged['timestamp'], unit='s')
    buy_grouped = buy_merged.groupby('timestamp')['buy_sum'].sum().reset_index()
    sell_grouped = sell_merged.groupby('timestamp')['sell_sum'].sum().reset_index()
    buy_sell_df = pd.merge(buy_grouped, sell_grouped, on='timestamp', how='outer')
    buy_sell_df.fillna(0, inplace=True)
    return buy_sell_df


def profit_today(exchange, symbol):
    now = datetime.datetime.now()
    midnight = (now - datetime.timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
    ts = int(midnight.timestamp()) * 1000
    profits = 0
    if isinstance(exchange, ccxt.binance):
        trades = exchange.fetch_my_trades(symbol=symbol, since=ts)
        for trade in trades:
            pnl = float(trade['info']['realizedPnl'])
            fees = trade['fee']['cost']
            profits = profits + pnl - fees
    if isinstance(exchange, ccxt.gateio):
        trades = exchange.fetch_closed_orders(symbol=symbol, since=ts)
        for trade in trades:
            pnl = float(trade['info']['pnl'])
            profits = profits + pnl
    return profits


def signal():
    global last_signal
    global order_imbalance_deque, liquidity_imbalance_deque, depth_ratio_deque
    bid, ask, mp_last = order_book()
    # 获取过去10s的订单簿(和上面一样)
    buy_sell_df = recent_trades()
    buy_sum_list, sell_sum_list = buy_sell_df['buy_sum'], buy_sell_df['sell_sum']
    buy, sell = sum(buy_sum_list), sum(sell_sum_list)

    # 过去10秒买量-卖量的差
    order_imbalance = buy - sell
    # 当前时刻获取到的买量-卖量的差
    liquidity_imbalance = bid - ask

    depth_ratio = (bid + ask) / (buy + sell)  # 深度vs交易量

    # try:
    #     with open('ba_bs_mp.csv', 'a', newline='') as csvfile:
    #         csvwriter = csv.writer(csvfile)
    #         csvwriter.writerow(
    #             [datetime.datetime.now(), bid, ask, buy_sum_list.iloc[-1], sell_sum_list.iloc[-1], mp_last])
    # except Exception as file_error:
    #     print("write ba_bs_mp error: {}".format(file_error))

    # 订单不平衡
    order_imbalance_deque.append(order_imbalance)
    # 流动性不平衡
    liquidity_imbalance_deque.append(liquidity_imbalance)
    depth_ratio_deque.append(depth_ratio)


    # 数据偏离程度(类似布林带)
    def mean_std(data_list, std_window):
        mean = np.mean(data_list)
        std = np.std(data_list)
        top = mean + std_window * std
        bot = mean - std_window * std
        return top - data_list[-1], data_list[-1] - bot

    top_oi, oi_bot = mean_std(order_imbalance_deque, 2)
    top_li, li_bot = mean_std(liquidity_imbalance_deque, 2)
    top_dr, dr_bot = mean_std(depth_ratio_deque, 1)

    open_long_signal = top_oi < 0 and top_li < 0 and dr_bot < 0
    open_short_signal = oi_bot < 0 and li_bot < 0 and dr_bot < 0
    dt = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print('{}\n{:.2f}, {:.2f}\n{:.2f}, {:.2f}\n{:.2f}'.format(
        dt, top_oi, oi_bot, top_li, li_bot, dr_bot))

    if open_long_signal:
        last_signal = 'buy'
        try:
            insert_signal_to_mysql((dt, bid, ask, buy, sell, mp_last, 1))
        except Exception as insert_signal_to_mysql_error:
            print('insert_signal_to_mysql_error: {}'.format(insert_signal_to_mysql_error))

    if open_short_signal:
        last_signal = 'sell'
        try:
            insert_signal_to_mysql((dt, bid, ask, buy, sell, mp_last, 2))
        except Exception as insert_signal_to_mysql_error:
            print('insert_signal_to_mysql_error: {}'.format(insert_signal_to_mysql_error))

    return open_long_signal, open_short_signal


def calculate_profits(exchange, symbol, from_date, quote_cash, initial_cash):
    profits = pd.read_csv('./{}_profits.csv'.format(ex_name(exchange)))
    last_date = datetime.datetime.strptime(profits['datetime'].iloc[-1], '%Y/%m/%d')
    today = datetime.datetime.now()

    if today - last_date >= datetime.timedelta(days=1):
        days_held = (today - from_date).days
        balance = exchange.fetch_balance()['total'][quote_cash]
        today_profit = profit_today(exchange, symbol)
        total_profit = balance - initial_cash
        today_annul_rate = (1 + today_profit / initial_cash) ** 365 - 1
        total_annul_rate = (1 + total_profit / initial_cash) ** (365 / days_held) - 1

        new_row = [today.strftime('%Y/%m/%d'), today_profit, balance, today_annul_rate, total_annul_rate]
        with open('{}_profits.csv'.format(ex_name(exchange)), 'a', newline='') as file:
            writer = csv.writer(file)
            writer.writerow(new_row)

        msg_cn = '{} 交易信号: 账户余额: {:.4f}, 今日收益: {:.4f}, 今日年化收益: {:.4%}, 年化总收益: {:.4%}'.format(
            ex_name(exchange), balance, today_profit, today_annul_rate, total_annul_rate)
        msg_en = ('{} Trade Signal: Total Balance: {:.4f}, Profit Today: {:.4f}, Annul Profit Today: {:.4%}, '
                  'Annul Profit Total: {:.4%}').format(
            ex_name(exchange), balance, today_profit, today_annul_rate, total_annul_rate)

        sql_data = (today, ex_name(exchange), Decimal(balance), Decimal(today_profit), Decimal(today_annul_rate),
                    Decimal(total_annul_rate))
        insert_profits_to_mysql(sql_data)


def open_long_order(exchange, amount, symbol):
    ol = open_long(exchange, amount, symbol)
    print(ex_name(exchange), '\n', ol)
    if ol is not None:
        ol_dt = ol['datetime'].replace('T', ' ').replace('Z', '')
        ol_id = ol['id']
        ol_price = ol['price']
        ol_status = ol['status']

        msg_cn = f'{ex_name(exchange)} 交易信号: 以{ol_price}开多{amount}BTC, 多仓{posi(exchange, symbol)[0]}'
        msg_en = f'{ex_name(exchange)} Trade Signal: Open Long {amount} BTC at {ol_price}'

        sql_data = (ex_name(exchange),
                    symbol,
                    'open_long',
                    Decimal(amount),
                    ol_dt,
                    Decimal(ol_price),
                    Decimal(0),
                    Decimal(0),
                    ol_status,
                    None,
                    None,
                    None)
        insert_trades_to_mysql(sql_data)


def close_long_order(exchange, amount, symbol, cash):
    today_pnl = profit_today(exchange, symbol)
    balance = exchange.fetch_balance()['total'][cash]
    cl = close_long(exchange, amount, symbol)
    print(ex_name(exchange), '\n', cl)
    if cl is not None:
        cl_dt = cl['datetime'].replace('T', ' ').replace('Z', '')
        cl_id = cl['id']
        cl_price = cl['price']
        long_pnl = 0
        cl_status = exchange.fetch_order_status(id=cl_id, symbol=symbol)
        if cl_status == 'closed':
            if isinstance(exchange, ccxt.gateio):
                cl_trade = exchange.fetch_order(symbol=symbol, id=cl_id)
                long_pnl = float(cl_trade['info']['pnl'])
            elif isinstance(exchange, ccxt.binance):
                cl_trade = exchange.fetch_my_trades(symbol=symbol, params={'orderId': cl_id})
                if len(cl_trade) != 0:
                    long_pnl = float(cl_trade[0]['info']['realizedPnl']) - cl_trade[0]['fee'][
                        'cost']
        msg_cn = '{}交易信号: 以{}平多{}BTC, 实现盈亏{:.4f}, 多仓剩余{}, 今日盈亏{:.4f}, 账户资产{:.4f}'.format(
            ex_name(exchange), cl['price'], cl['amount'], long_pnl, posi(exchange, symbol)[0], today_pnl, balance)
        msg_en = '{} Trade Signal: Close Long {} BTC at {}, Realized Profit {:.4f}, Long Position {}'.format(
            ex_name(exchange), cl['amount'], cl['price'], long_pnl, posi(exchange, symbol)[0])

        sql_data = (ex_name(exchange),
                    symbol,
                    'close_long',
                    Decimal(amount),
                    cl_dt,
                    Decimal(cl_price),
                    Decimal(0),
                    Decimal(0),
                    cl_status,
                    None,
                    None,
                    None)
        insert_trades_to_mysql(sql_data)


def open_short_order(exchange, amount, symbol):
    os = open_short(exchange, amount, symbol)
    print(ex_name(exchange), '\n', os)
    if os is not None:
        os_dt = os['datetime'].replace('T', ' ').replace('Z', '')
        os_id = os['id']
        os_price = os['price']
        os_status = os['status']

        msg_cn = f'{ex_name(exchange)} 交易信号: 以{os_price}开空{amount}BTC, 空仓{posi(exchange, symbol)[1]}'
        msg_en = f'{ex_name(exchange)} Trade Signal: Open Short {amount} BTC at {os_price}'

        sql_data = (ex_name(exchange),
                    symbol,
                    'open_short',
                    Decimal(amount),
                    os_dt,
                    Decimal(os_price),
                    Decimal(0),
                    Decimal(0),
                    os_status,
                    None,
                    None,
                    None)
        insert_trades_to_mysql(sql_data)


def close_short_order(exchange, amount, symbol, cash):
    today_pnl = profit_today(exchange, symbol)
    balance = exchange.fetch_balance()['total'][cash]
    cs = close_short(exchange, amount, symbol)
    print(ex_name(exchange), '\n', cs)
    if cs is not None:
        cs_dt = cs['datetime'].replace('T', ' ').replace('Z', '')
        cs_id = cs['id']
        cs_price = cs['price']
        cs_status = exchange.fetch_order_status(id=cs_id, symbol=symbol)
        short_pnl = 0
        if cs_status == 'closed':
            if isinstance(exchange, ccxt.gateio):
                cs_trade = exchange.fetch_order(symbol=symbol, id=cs_id)
                short_pnl = float(cs_trade['info']['pnl'])
            elif isinstance(exchange, ccxt.binance):
                cs_trade = exchange.fetch_my_trades(symbol=symbol, params={'orderId': cs_id})
                if len(cs_trade) != 0:
                    short_pnl = float(cs_trade[0]['info']['realizedPnl']) - cs_trade[0]['fee'][
                        'cost']
        msg_cn = '{} 交易信号: 以{}平空{}BTC, 实现盈亏{:.4f}, 空仓剩余{}, 今日盈亏{:.4f}, 账户资产{:.4f}'.format(
            ex_name(exchange), cs['price'], cs['amount'], short_pnl, posi(exchange, symbol)[1], today_pnl,
            balance)
        msg_en = '{} Trade Signal: Close Short {} BTC at {}, Realized Profit {:.4f}, Short Position {}'.format(
            ex_name(exchange), cs['amount'], cs['price'], short_pnl, posi(exchange, symbol)[1])

        sql_data = (ex_name(exchange),
                    symbol,
                    'close_short',
                    Decimal(amount),
                    cs_dt,
                    Decimal(cs_price),
                    Decimal(0),
                    Decimal(0),
                    cs_status,
                    None,
                    None,
                    None)
        insert_trades_to_mysql(sql_data)


def main_strategy(exchange, amount, symbol, cash):
    open_long_signal, open_short_signal = signal()
    long_posi, short_posi, long_unrppct, short_unrppct, _, long_entryprice, short_entryprice = posi(exchange, symbol)
    balance = exchange.fetch_balance()['total'][cash]
    long_margin = long_posi * long_entryprice / leve
    short_margin = short_posi * short_entryprice / leve

    if open_long_signal:
        print(f"{ex_name(exchange)}_open_long_signal")
        close_short_order(exchange, amount, symbol, cash)
        if long_margin < balance:  # / long_posi_rate
            open_long_order(exchange, amount, symbol)

    if open_short_signal:
        print(f"{ex_name(exchange)}_open_short_signal")
        close_long_order(exchange, amount, symbol, cash)
        if short_margin < balance:  # / short_posi_rate
            open_short_order(exchange, amount, symbol)

