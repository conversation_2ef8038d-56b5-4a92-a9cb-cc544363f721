"""
币安U本位期货资金费率历史数据获取工具

使用方法：
1. 设置symbol、时间范围等参数
2. 运行程序获取资金费率历史数据
3. 数据保存为CSV格式到data目录

API限制：
- 默认返回最近100条数据，最大1000条
- 如果指定时间范围，按时间升序返回
"""

import aiohttp
from itertools import chain
import asyncio
import pandas as pd
import os
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Binance U本位期货资金费率API
BINANCE_FUNDING_RATE_URL = "https://fapi.binance.com/fapi/v1/fundingRate"

# 数据保存配置
DATA_DIR = "data"

# 代理配置
PROXY_CONFIG = {
    "http": "http://127.0.0.1:7890"
}

# 频率限制配置
RATE_LIMIT_CONFIG = {
    "requests_per_batch": 100,      # 每批次请求数量
    "delay_between_batches": 10,   # 批次间延迟（秒）
    "request_delay": 0.2,          # 单个请求间延迟（秒）
    "retry_delay": 5,              # 遇到限制时的重试延迟（秒）
    "max_retries": 3               # 最大重试次数
}

def ensure_data_directory():
    """确保data目录存在"""
    if not os.path.exists(DATA_DIR):
        os.makedirs(DATA_DIR)
        logging.info(f"Created data directory: {DATA_DIR}")

def get_filename(symbol, start_date=None, end_date=None):
    """生成文件名"""
    if start_date and end_date:
        return f"{symbol}_fundingrate_{start_date}_{end_date}.csv"
    else:
        return f"{symbol}_fundingrate_latest.csv"

async def fetch_funding_rate(session, symbol, start_time=None, end_time=None, limit=1000):
    """
    获取资金费率数据

    Args:
        session: aiohttp session
        symbol: 交易对符号，如 'BTCUSDT'
        start_time: 开始时间戳(毫秒)，可选
        end_time: 结束时间戳(毫秒)，可选
        limit: 返回数据条数，默认1000，最大1000
    """
    params = {
        "symbol": symbol,
        "limit": limit
    }

    if start_time:
        params["startTime"] = start_time
    if end_time:
        params["endTime"] = end_time

    for attempt in range(RATE_LIMIT_CONFIG["max_retries"] + 1):
        try:
            # 添加请求间延迟
            await asyncio.sleep(RATE_LIMIT_CONFIG["request_delay"])

            async with session.get(BINANCE_FUNDING_RATE_URL, params=params, proxy=PROXY_CONFIG["http"]) as response:
                # 检查是否遇到频率限制
                if response.status == 429:
                    retry_after = int(response.headers.get('Retry-After', RATE_LIMIT_CONFIG["retry_delay"]))
                    logging.warning(f"触发频率限制，等待{retry_after}秒后重试")
                    await asyncio.sleep(retry_after)
                    continue

                response.raise_for_status()
                data = await response.json()

                # 检查是否有错误响应
                if isinstance(data, dict) and 'code' in data:
                    if data.get('code') == -1121:  # Invalid symbol
                        logging.error(f"Invalid symbol: {data}")
                        return []
                    elif data.get('code') == -1003:  # Too many requests
                        logging.warning(f"请求过多，重试中...")
                        await asyncio.sleep(RATE_LIMIT_CONFIG["retry_delay"])
                        continue
                    else:
                        logging.error(f"API Error: {data}")
                        return []

                # 成功获取数据
                return data

        except aiohttp.ClientError as e:
            if attempt < RATE_LIMIT_CONFIG["max_retries"]:
                await asyncio.sleep(RATE_LIMIT_CONFIG["retry_delay"])
            else:
                logging.error(f"请求失败，已重试{RATE_LIMIT_CONFIG['max_retries']}次: {e}")
                return []
        except Exception as e:
            logging.error(f"Unexpected error in fetch_funding_rate: {e}")
            return []

    return []

def save_funding_rate_data(filename, funding_data):
    """将资金费率数据保存到CSV文件"""
    try:
        if not funding_data:
            logging.warning("没有数据需要保存")
            return

        # 创建DataFrame
        df = pd.DataFrame(funding_data)

        # 转换时间戳为本地时间格式（保持币安原始时区UTC+8）
        df['fundingTime'] = pd.to_datetime(df['fundingTime'], unit='ms') + pd.Timedelta(hours=8)
        df['fundingTime'] = df['fundingTime'].dt.strftime('%Y-%m-%d %H:%M:%S')

        # 设置索引为fundingTime
        df.set_index('fundingTime', inplace=True)

        # 按时间排序
        df = df.sort_index()

        # 保存到文件
        filepath = os.path.join(DATA_DIR, filename)
        df.to_csv(filepath)

        logging.info(f"已保存 {len(funding_data)} 条资金费率数据到 {filepath}")

    except Exception as e:
        logging.error(f"保存数据失败: {e}")

def generate_time_ranges(start_date, end_date, days_per_batch=7):
    """
    生成时间范围列表，用于分批获取历史数据

    Args:
        start_date: 开始日期 (datetime)
        end_date: 结束日期 (datetime)
        days_per_batch: 每批次天数，默认7天（更小的时间段以便并发）

    Returns:
        List of (start_timestamp_ms, end_timestamp_ms) tuples
    """
    time_ranges = []
    current_start = start_date

    while current_start < end_date:
        current_end = min(current_start + timedelta(days=days_per_batch), end_date)

        start_ms = int(current_start.timestamp() * 1000)
        end_ms = int(current_end.timestamp() * 1000)

        time_ranges.append((start_ms, end_ms))
        current_start = current_end

    return time_ranges

async def fetch_historical_funding_rate(symbol, start_year, end_year):
    """
    获取指定年份范围的历史资金费率数据

    Args:
        symbol: 交易对符号，如 'BTCUSDT'
        start_year: 开始年份 (int)，如 2020
        end_year: 结束年份 (int)，如 2024
    """
    # 转换年份为日期对象
    start_date = datetime(start_year, 1, 1)
    end_date = datetime(end_year + 1, 1, 1)  # 包含整个end_year年份

    # 确保不超过当前时间
    current_time = datetime.now()
    if end_date > current_time:
        end_date = current_time
    ensure_data_directory()

    # 生成文件名
    filename = get_filename(symbol, str(start_year), str(end_year))

    logging.info(f"开始获取 {symbol} 资金费率历史数据 ({start_year}-{end_year})")

    # 生成时间范围
    time_ranges = generate_time_ranges(start_date, end_date)
    total_requests = len(time_ranges)
    batch_size = RATE_LIMIT_CONFIG["requests_per_batch"]
    total_batches = (total_requests + batch_size - 1) // batch_size

    logging.info(f"开始获取数据: {total_requests}个时间段, {total_batches}批次, 预计{total_batches * RATE_LIMIT_CONFIG['delay_between_batches'] / 60:.1f}分钟")

    all_data = []

    # 创建连接器
    connector = aiohttp.TCPConnector(use_dns_cache=False)
    async with aiohttp.ClientSession(connector=connector) as session:
        tasks = []
        batch_count = 0

        for i, (start_ms, end_ms) in enumerate(time_ranges):
            # 添加任务到批次
            tasks.append(fetch_funding_rate(session, symbol, start_ms, end_ms))

            # 当达到批次大小或是最后一个请求时，执行批次
            if (i + 1) % batch_size == 0 or i == len(time_ranges) - 1:
                batch_count += 1
                logging.info(f"批次 {batch_count}/{total_batches} ({len(tasks)}个请求)")

                # 并发执行当前批次的所有任务
                results = await asyncio.gather(*tasks)
                batch_data = list(chain.from_iterable(results))
                all_data.extend(batch_data)

                logging.info(f"完成 {batch_count}/{total_batches}, 获得{len(batch_data)}条数据, 总计{len(all_data)}条")

                # 清空任务列表
                tasks = []

                # 添加延迟以避免频率限制（除非是最后一批）
                if batch_count < total_batches:
                    logging.info(f"等待{RATE_LIMIT_CONFIG['delay_between_batches']}秒...")
                    await asyncio.sleep(RATE_LIMIT_CONFIG["delay_between_batches"])

    # 保存所有数据
    if all_data:
        # 去重（基于symbol和fundingTime）
        seen = set()
        unique_data = []
        for item in all_data:
            key = (item['symbol'], item['fundingTime'])
            if key not in seen:
                seen.add(key)
                unique_data.append(item)

        logging.info(f"去重后共 {len(unique_data)} 条数据")
        save_funding_rate_data(filename, unique_data)
    else:
        logging.warning("未获取到任何数据!")

async def fetch_latest_funding_rate(symbol, limit=100):
    """
    获取最新的资金费率数据

    Args:
        symbol: 交易对符号，如 'BTCUSDT'
        limit: 返回数据条数，默认100
    """
    ensure_data_directory()

    filename = get_filename(symbol)
    logging.info(f"获取 {symbol} 最新 {limit} 条资金费率数据")

    connector = aiohttp.TCPConnector(use_dns_cache=False)
    async with aiohttp.ClientSession(connector=connector) as session:
        data = await fetch_funding_rate(session, symbol, limit=limit)

        if data:
            save_funding_rate_data(filename, data)
        else:
            logging.warning("未获取到数据!")

async def fetch_usdt_funding_rate(symbol, start_year, end_year):
    """
    便捷函数：获取U本位期货资金费率数据

    Args:
        symbol: U本位合约符号，如 'BTCUSDT', 'ETHUSDT'
        start_year: 开始年份
        end_year: 结束年份
    """
    logging.info(f"开始获取U本位期货资金费率数据: {symbol}")
    await fetch_historical_funding_rate(symbol, start_year, end_year)

async def main(symbol, start_year, end_year):
    """
    主函数 - 获取指定年份范围的资金费率数据

    Args:
        symbol: 交易对符号，如 'BTCUSDT'
        start_year: 开始年份，如 2020
        end_year: 结束年份，如 2024
    """
    await fetch_historical_funding_rate(symbol, start_year, end_year)

if __name__ == "__main__":
    # 在Windows上设置事件循环策略以避免aiodns问题
    if os.name == 'nt':  # Windows
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

    # ========== 配置参数 ==========
    symbol = "ETHUSDT"  # 交易对符号
    start_year = 2020   # 开始年份（先测试小范围）
    end_year = 2025     # 结束年份

    # 运行主程序
    asyncio.run(main(symbol, start_year, end_year))



    # ========== 使用示例 ==========
    """
    # 示例1: 获取BTCUSDT 2020-2024年的资金费率历史数据
    symbol = "BTCUSDT"
    start_year = 2020
    end_year = 2024
    asyncio.run(main(symbol, start_year, end_year))

    # 示例2: 获取ETHUSDT最新1000条资金费率数据
    symbol = "ETHUSDT"
    asyncio.run(fetch_latest_funding_rate(symbol, 1000))

    # 示例3: 获取SOLUSDT 2023年的资金费率数据
    symbol = "SOLUSDT"
    start_year = 2023
    end_year = 2023
    asyncio.run(main(symbol, start_year, end_year))
    """