import pickle
import re
from sqlalchemy import create_engine
from pandas.errors import PerformanceWarning
import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
import scipy.stats as stats
import warnings
from sklearn.linear_model import LinearRegression
warnings.filterwarnings(
    "ignore", message=".*swapaxes.*", category=FutureWarning)

warnings.simplefilter("ignore", PerformanceWarning)

MYSQL_CONFIG = {
    "host": "************",
    "user": "root",
    "password": "1234",
    "database": "ctadata"
}


class DataManager():

    def __init__(self):
        # self.__tim__ = ticker.TickerInfoManager()

        self.problem_ticker = []
        self.engine = create_engine(f"mysql+pymysql://{MYSQL_CONFIG['user']}:{
                                    MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}/{MYSQL_CONFIG['database']}")
        return

    def concat_data(self, data):
        """读取并合并单品类永续合约的K线数据"""
        query = f"SELECT * FROM {data.contract}"
        df = pd.read_sql(query, self.engine)
        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
        df.set_index('open_time', inplace=True)
        # 将所有K线数据合并
        data.data = df
        return


class Data():

    def __init__(self, contract):
        self.contract = contract
        return


def smma(series, length):
    """ 计算平滑移动平均 (SMMA) """
    smma_values = series.ewm(alpha=1/length, adjust=False).mean()
    return smma_values


def emma(series, length):
    """ 计算指数加权移动平均 (EMMA) """
    emma_values = series.ewm(span=length, adjust=False).mean()
    return emma_values


def sma(series, N, M):
    sma_values = series.copy()
    sma_values.iloc[0] = series.iloc[:N].mean()  # 初始化第一个SMA值（可选）

    for i in range(1, len(series)):
        sma_values.iloc[i] = (series.iloc[i] * M +
                              sma_values.iloc[i-1] * (N - M)) / N

    return sma_values


def rolling_low(series, p):
    """计算 t-2p 到 t-1 的最小值，确保不使用未来数据"""
    return series.shift(1).rolling(window=2*p, min_periods=1).min()


def rolling_high(series, p):
    """计算 t-2p 到 t-1 的最大值，确保不使用未来数据"""
    return series.shift(1).rolling(window=2*p, min_periods=1).max()


def rsi_deviation(df, rsi_col='rsi', price_col='close', p=3, signal_shift=None):
    """计算 RSI 背离因子（不使用未来数据）"""
    df = df.copy()

    # 如果 signal_shift 为空，默认使用 t-(p+1) 作为信号点
    if signal_shift is None:
        signal_shift = p + 1

    # 计算局部高低点（基于 t-2p 到 t-1）
    df['RollingLow'] = rolling_low(df[price_col], p)
    df['RollingHigh'] = rolling_high(df[price_col], p)
    df['RSI_Low'] = rolling_low(df[rsi_col], p)
    df['RSI_High'] = rolling_high(df[rsi_col], p)

    # 牛市背离：价格创新低，RSI 没创新低
    price_condition_bullish = df[price_col].shift(
        signal_shift) <= df['RollingLow'].shift(signal_shift)
    rsi_condition_bullish = df[rsi_col].shift(
        signal_shift-1) >= df['RSI_Low'].shift(signal_shift)

    bullish_strength = np.where(
        price_condition_bullish & rsi_condition_bullish,
        abs(df[price_col].shift(signal_shift) - df['RollingLow'].shift(signal_shift)) *
        abs(df[rsi_col].shift(signal_shift-1) -
            df['RSI_Low'].shift(signal_shift)),
        0
    )

    # 熊市背离：价格创新高，RSI 没创新高
    price_condition_bearish = df[price_col].shift(
        signal_shift) >= df['RollingHigh'].shift(signal_shift)
    rsi_condition_bearish = df['RSI_High'].shift(
        signal_shift) >= df[rsi_col].shift(signal_shift-1)

    bearish_strength = np.where(
        price_condition_bearish & rsi_condition_bearish,
        -abs(df['RollingHigh'].shift(signal_shift) - df[price_col].shift(signal_shift)) *
        abs(df['RSI_High'].shift(signal_shift) -
            df[rsi_col].shift(signal_shift-1)),
        0
    )

    # 计算 RSI 背离因子
    df['RSI_Divergence_Factor'] = bullish_strength + bearish_strength

    return df['RSI_Divergence_Factor']


def chao2_factor(df):
    # 根据 RSI 判断市场是否超买（高）或超卖（低）
    overbought = df['rsi'] > 70
    oversold = df['rsi'] < 30

    # 根据 ATR 判断市场波动性
    high_volatility = df['atr'] > df['atr'].rolling(
        window=20).mean()  # ATR大于20日均值为高波动

    # 根据 Volume ROC 判断交易量变化率
    high_volume_change = df['volume_roc'] > 0.1  # 假设Volume ROC大于0.1为显著变化

    # 初始化因子列
    df['chao2'] = 0

    # 超买且高波动、高交易量：可能意味着回调，赋负权重
    df.loc[overbought & high_volatility & high_volume_change, 'chao2'] = -1

    # 超卖且高波动、高交易量：可能意味着反转，赋正权重
    df.loc[oversold & high_volatility & high_volume_change, 'chao2'] = 1

    # 超买且低波动或交易量变化不大，市场趋于稳定，不赋权重
    df.loc[overbought & ~high_volatility | ~high_volume_change, 'chao2'] = 0

    # 超卖且低波动或交易量变化不大，市场趋于稳定，不赋权重
    df.loc[oversold & ~high_volatility | ~high_volume_change, 'chao2'] = 0

    return df['chao2']


def alpha_009(df):
    # 计算核心部分
    mid_price = (df['high'] + df['low']) / 2
    delay_mid_price = (df['high'].shift(1) + df['low'].shift(1)) / 2
    volatility = (df['high'] - df['low']) / df['volume']

    # 避免除0
    df['factor'] = (mid_price - delay_mid_price) / volatility.replace(0, 1)

    # 计算加权移动平均（SMA 7,2）
    df['alpha_x'] = sma(df['factor'], 7, 2)

    return df['alpha_x']


def analyze_extreme_values(df, col='HV'):
    """头尾部超额分析"""
    q10, q90 = np.percentile(df[col].dropna(), [10, 90])  # 计算 10% 和 90% 分位数

    # 取头部和尾部数据
    tail_data = df[df[col] <= q10]
    head_data = df[df[col] >= q90]

    # 计算统计描述
    tail_stats = tail_data.describe()
    head_stats = head_data.describe()

    return tail_stats, head_stats


class BollingerBand:
    def __init__(self, c, p=20, k=2):
        self.c = c
        self.p = p
        self.k = k
        self.ma = None
        self.upper_band = None
        self.lower_band = None
        self.calculate_bollinger_bands()

    def calculate_bollinger_bands(self):
        self.ma = self.c.rolling(window=self.p).mean()  # 中轨
        rolling_std = self.c.rolling(window=self.p).std()  # 滚动标准差
        self.upper_band = self.ma + (rolling_std * self.k)  # 上轨
        self.lower_band = self.ma - (rolling_std * self.k)  # 下轨

    def get_bollinger_bands(self):
        """返回布林带计算结果"""
        return {
            'ma': self.ma,
            'upper_band': self.upper_band,
            'lower_band': self.lower_band
        }


def bollinger_breakout_downward(d):
    c = d.close
    h = d.high
    l = d.low
    new_bollinger = BollingerBand(c)
    bollinger = new_bollinger.get_bollinger_bands()
    rolling_mean = bollinger["ma"]
    lower_band = bollinger["lower_band"]

    long_T = 100
    short_T = 20
    ma_long = c.rolling(window=long_T).mean()
    ma_short = c.rolling(window=short_T, min_periods=15).mean()

    ma = ma_short / ma_long
    ma_min = ma.expanding().quantile(0.2)

    # 计算最高价最低价突破布林带
    lower_bt = l < lower_band

    # 判断连续最高价最低价突破布林带
    window_size = 4
    lower_ct = lower_bt.rolling(window=window_size).apply(
        lambda x: all(x), raw=True)

    # open_type 0不开仓,1为开多,-1为开空
    open_type = 0
    open_list = []
    for idx in range(len(d)):
        if idx >= 3:
            if lower_ct.iloc[idx]:
                open_type = 1

        if open_type == 1 and (h.iloc[idx] > rolling_mean.iloc[idx]).any() and ma.iloc[idx] < ma_min.iloc[idx]:
            open_list.append(open_type)
            open_type = 0
        else:
            open_list.append(0)

    s = pd.Series(open_list)
    return s


def analyze_full_range(df, col='HV', bins=10):
    """对 0%-100% 之间的数据分成 10 组，并计算统计信息"""
    # 不再过滤数据，直接对所有数据进行处理
    full_data = df[col].dropna()

    # 使用 pd.qcut() 将数据分成 bins 组
    full_data_grouped = pd.qcut(full_data, bins, labels=False)

    # 将分组信息添加到原数据
    df['group'] = pd.qcut(df[col], bins, labels=False)

    # 计算每组的描述性统计信息
    grouped_stats = df.groupby('group')[col].describe()

    return grouped_stats


def plot_group_means(group_means: pd.Series, n_groups: int, title: str):
    """绘制分组均值收益条形图"""
    group_means.plot(kind='bar', color='skyblue', figsize=(12, 6))
    plt.title(title)
    plt.xlabel('Group')
    plt.ylabel('Average Return')
    plt.xticks(rotation=0)
    # 自动调整 y 轴范围
    # 自动根据数据的最小和最大值设置 y 轴范围
    # plt.ylim(group_means.min() - 0.02, group_means.max() + 0.02)

    plt.show(block=False)


def calculate_and_plot_group_means(df: pd.DataFrame, factor_col: str, return_col: str, n_groups: int):
    """根据因子列对数据分组，并计算每组的收益均值，绘制条形图"""

    # 检查因子列和收益列是否有缺失值
    if df[[factor_col, return_col]].isna().sum().any():
        print("因子列或收益列存在缺失值，正在删除缺失值行...")
        df = df.dropna(subset=[factor_col, return_col])

    # 将因子列分成 n_groups 组，qcut 会按照分位数分组
    groups = pd.qcut(df[factor_col], n_groups, labels=False, duplicates='drop')

    # 计算每个分组的均值
    group_means = df.groupby(groups)[return_col].mean()

    # 打印每组均值
    print(f"每组的均值：\n{group_means}")

    # 绘制每个分组的均值条形图
    plt.figure(figsize=(8, 6))  # 创建新的 figure
    plot_group_means(group_means, n_groups, f'{factor_col} group_return')


def c_chu030(df):
    '''计算k线距离因子'''
    kline_distance = (2*(df['high'] - df['low']) -
                      abs(df['close']-df['open']))/df['volume']
    return kline_distance


if __name__ == '__main__':

    # df = pd.DataFrame()
    # currency = 'BTCUSDT_15m_2020_2025 '
    # d = Data(currency)
    # dm = DataManager()
    # dm.concat_data(d)
    # df['BTC_r96'] = d.data['close'].shift(-96)/d.data['close']-1
    # currency = 'ETHUSDT_15m_2020_2025 '
    # d = Data(currency)
    # dm = DataManager()
    # dm.concat_data(d)
    # df['ETH_r96'] = d.data['close'].shift(-96)/d.data['close']-1
    # currency = 'SOLUSDT_15m_2020_2025 '
    # d = Data(currency)
    # dm = DataManager()
    # dm.concat_data(d)
    # df['SOL_r96'] = d.data['close'].shift(-96)/d.data['close']-1
    # currency = 'DOGEUSDT_15m_2020_2025 '
    # d = Data(currency)
    # dm = DataManager()
    # dm.concat_data(d)
    # df['DOGE_r96'] = d.data['close'].shift(-96)/d.data['close']-1
    # currency = 'XRPUSDT_15m_2020_2025 '
    # d = Data(currency)
    # dm = DataManager()
    # dm.concat_data(d)
    # df['XRP_r96'] = d.data['close'].shift(-96)/d.data['close']-1
    # currency = 'UNIUSDT_15m_2020_2025 '
    # d = Data(currency)
    # dm = DataManager()
    # dm.concat_data(d)
    # df['UNI_r96'] = d.data['close'].shift(-96)/d.data['close']-1
    # currency = 'BNBUSDT_15m_2020_2025 '
    # d = Data(currency)
    # dm = DataManager()
    # dm.concat_data(d)
    # df['BNB_r96'] = d.data['close'].shift(-96)/d.data['close']-1
    # df = df.dropna()
    # print(df.corr())
    # exit()

    currency = 'ETHUSDT_15m_2022_2025'
    d = Data(currency)
    dm = DataManager()
    dm.concat_data(d)
    # df = pd.DataFrame()
    # coinname = re.search(r'^(.*?)USDT', currency).group(1)
    # df[f'{coinname}_r96'] = d.data['close'].shift(-96)/d.data['close']-1
    # with open(f"C:/Users/<USER>/python sc/workspace/coins_data/{coinname}_r96.pkl", "wb") as f:
    #     pickle.dump(df, f)
    # print('over')
    # exit()
    # columns = ['open', 'high', 'low', 'close', 'volume', 'close_time', 'turnover',
    #            'trade_count', 'taker_buy_volume', 'taker_buy_turnover']
    # df = d.data.copy()
    # df['r150'] = df['close'].shift(-150)/df['close']-1
    # print(df.corr())
    # exit()
    # for column in columns:
    #     print(f'{column}:{d.data[column].corr(
    #         d.data['close'].shift(-150)/d.data['close']-1)}')
    # exit()

    for t in range(96, 96+1):

        # 向后移动t个周期的收益率,初始t=10
        df = d.data.copy()
        df['close_shifted'] = df['close'].shift(-t)
        df['return'] = (df['close_shifted'] - df['close']) / df['close']
        # print(df.head())

        # print(df.tail())
        # exit()
        #############################################
        # 计算rsi
        df['close_diff'] = df['close'].diff()
        # 计算 u 和 d
        df['u'] = np.where(df['close_diff'] > 0, df['close_diff'], 0)
        df['d'] = np.where(df['close_diff'] < 0, -df['close_diff'], 0)
        df['close_diff_percent'] = df['close_diff'] / df['close']
        length = 14
        # 计算
        df['smma_u'] = smma(df['u'], length)
        df['smma_d'] = smma(df['d'], length)
        # 计算 RSI14
        df['rsi'] = np.where(df['smma_d'] == 0, 100,
                             np.where(df['smma_u'] == 0, 0,
                                      100 * (df['smma_u'] / (df['smma_u'] + df['smma_d']))))
        df['rsi_diff'] = df['rsi'].diff()
        df['volume_roc'] = (
            df['volume']-df['volume'].shift(1))/df['volume'].shift(1)

        #############################################
        # 计算rsi背离因子
        df['rsi_deviation'] = rsi_deviation(df)
        #############################################
        # 计算rsi背离因子(改)
        df['ma_rsi'] = df['rsi'].rolling(5).mean()

        df['rsi_deviation_moded'] = 0  # 初始化为 0
        df.loc[(df['ma_rsi'] < df['ma_rsi'].shift(1)) & (
            df['close'] > df['close'].shift(1)), 'rsi_deviation_moded'] = -1
        df.loc[(df['ma_rsi'] > df['ma_rsi'].shift(1)) & (
            df['close'] < df['close'].shift(1)), 'rsi_deviation_moded'] = 1
        #############################################
        # 计算真实波动幅度（tr）
        df['tr1'] = df['high'] - df['low']
        df['tr2'] = abs(df['high'] - df['close'].shift(1))  # 参考前一周期的收盘价
        df['tr3'] = abs(df['close'].shift(1) - df['low'])
        df['tr'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)

        window = 14

        # 计算 atr
        df['atr'] = smma(df['tr'], window)

        q1 = df['atr'].quantile(0.33)  # 33% 分位数
        q2 = df['atr'].quantile(0.66)  # 66% 分位数

        #############################################
        # 计算vwap
        df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3
        df['vwap'] = (df['typical_price'] * df['volume']).cumsum() / \
            df['volume'].cumsum()

        #############################################
        # 计算vwap背离因子
        df['vwap_deviation'] = (df['close'] - df['vwap']) / df['vwap']

        #############################################
        # 计算chao1因子
        df['chao1'] = ((df['close']-df['close'].shift(1))) / \
            (df['volume']-df['volume'].shift(1))
        # 去除无穷大和无穷小
        # df = df.replace([np.inf, -np.inf],
        #                 np.nan).dropna(subset=['chao1', 'return'])

        #############################################
        # 计算chao2因子
        df['chao2'] = chao2_factor(df)

        #############################################
        # 计算chao3因子
        df['body_ratio'] = (df['close']-df['open']) / (df['high']-df['low'])
        df['body_ratio_ema20'] = emma(df['body_ratio'], 20)
        df['chao3'] = df['body_ratio_ema20']

        #############################################
        # 计算chao4因子
        df['ma1'] = smma(df['close'], 20)
        df['ma2'] = smma(df['close'], 50)
        df['ma3'] = smma(df['close'], 100)

        df['chao4'] = np.where(
            (df['ma1'] > df['ma2']) & (df['ma2'] > df['ma3']) &
            (df['close'] < df['ma2']) & (df['close'] > df['ma3']),
            1,
            np.where(
                (df['ma1'] < df['ma2']) & (df['ma2'] < df['ma3']) &
                (df['close'] > df['ma2']) & (df['close'] < df['ma3']),
                -1,
                0

            )
        )

        #############################################
        # 计算chao5因子
        df['chao5'] = np.sqrt(df['high'] * df['low'])-df['vwap']

        #############################################
        # 计算chao6因子(一坨)
        df['chao6'] = df['close'].diff().rolling(
            20).corr(df['volume'].diff())

        #############################################
        # 计算HV因子
        df['HV'] = np.log((df['close']/df['close'].shift(1))
                          ).rolling(20).std()
        log_ratio = np.log(df['close']/df['close'].shift(1))
        df['HV2'] = log_ratio.rolling(20).apply(
            lambda x: x[x > 0].std(), raw=False)
        # df.loc[df['HV'] > 0.0125, 'HV'] = 0.0125

        # 添加算子
        df['HV_cb'] = df['HV']**3
        df['HV_sq'] = df['HV']**2
        df['HV_sqrt'] = np.sqrt(df['HV'])
        df['HV_diff1'] = df['HV'].diff(1)
        df['HV_diff2'] = df['HV'].diff(2)
        df['HV_cumsum'] = df['HV'].cumsum()
        # 非线性变换
        df['HV_ln'] = np.log(df['HV'])
        df['HV_exp'] = np.exp(df['HV'])
        # 激活函数
        df['HV_sigmoid'] = 1 / (1 + np.exp(-df['HV']))
        df['HV_tanh'] = np.tanh(df['HV'])

        #############################################
        # 计算alpha_004因子
        df['alpha_004'] = np.where(
            (df['close'].rolling(8).mean() + df['close'].rolling(8).std()
             ) < df['close'].rolling(2).mean(), -1,
            np.where(
                df['close'].rolling(2).mean() < (df['close'].rolling(
                    8).mean() - df['close'].rolling(8).std()), 1,
                np.where(df['volume'] /
                         df['volume'].rolling(20).mean() >= 1, 1, -1)
            )
        )

        #############################################
        # 计算alpha_009因子
        df['alpha_009'] = alpha_009(df)

        #############################################
        # 计算alpha_010因子
        df['alpha_010'] = (((df['close'] - df['low']) - (df['high'] - df['close'])) /
                           (df['high'] - df['low']) * df['volume']).rolling(10).sum()

        #############################################
        # 计算alpha_021因子(一坨)
        df['mean_close_6'] = df['close'].rolling(10).mean()
        # 定义时间序列 [1,2,3,4,5,6]
        time_seq = np.arange(1, 11)
        # 计算 6 天窗口内的回归斜率
        df['alpha_021'] = df['mean_close_6'].rolling(10).apply(
            lambda y: stats.linregress(time_seq, y).slope, raw=True
        )

        #############################################
        # 计算alpha_022因子
        df['mean_close_6'] = df['close'].rolling(6).mean()

        # 计算标准化偏离
        df['deviation'] = (df['close'] - df['mean_close_6']
                           ) / df['mean_close_6']

        # 计算变化量
        df['deviation_change'] = df['deviation'] - df['deviation'].shift(1)

        # 计算 12 日滑动均值 (SMA)
        df['alpha_022'] = df['deviation_change'].ewm(
            span=12, adjust=False).mean()

        #############################################
        # 计算alpha_043因子
        df['price_change'] = df['close'].diff(1)  # 计算前一日的涨跌情况
        df['volume_contribution'] = np.where(df['price_change'] > 0, df['volume'],
                                             np.where(df['price_change'] < 0, -df['volume'], 0))
        df['alpha_043'] = df['volume_contribution'].rolling(6).sum()

        #############################################
        # 计算alpha_046因子
        df['alpha_046'] = (df['close'].rolling(3).mean() +
                           df['close'].rolling(6).mean() +
                           df['close'].rolling(12).mean() +
                           df['close'].rolling(24).mean()) / (4 * df['close'])

        #############################################
        # 计算alpha_050因子
        # 计算昨日 HIGH 和 LOW
        df['high_delay1'] = df['high'].shift(1)
        df['low_delay1'] = df['low'].shift(1)

        # 计算 HIGH + LOW 变动
        cond1 = (df['high'] + df['low']
                 ) <= (df['high_delay1'] + df['low_delay1'])
        cond2 = (df['high'] + df['low']
                 ) >= (df['high_delay1'] + df['low_delay1'])

        # 计算 |HIGH 变动| 和 |LOW 变动| 的最大值
        delta_high = abs(df['high'] - df['high_delay1'])
        delta_low = abs(df['low'] - df['low_delay1'])
        max_delta = np.maximum(delta_high, delta_low)

        # 计算 S1 和 S2
        df['S1'] = np.where(cond1, 0, max_delta)
        df['S2'] = np.where(cond2, 0, max_delta)

        # 计算 SUM(S1,12) 和 SUM(S2,12)
        sum_S1 = df['S1'].rolling(12).sum()
        sum_S2 = df['S2'].rolling(12).sum()

        # 计算最终因子值
        df['alpha_050'] = (sum_S1 / (sum_S1 + sum_S2)) - \
            (sum_S2 / (sum_S1 + sum_S2))

        #############################################
        # 计算alpha_057
        # 计算收盘价是否上涨
        # 计算 RSV
        df['rsv'] = (df['close'] - df['low'].rolling(window=9).min()) / \
                    (df['high'].rolling(window=9).max() -
                     df['low'].rolling(window=9).min()) * 100

        # 计算 SMA
        df['alpha_057'] = df['rsv'].rolling(window=3, min_periods=1).mean()
        #############################################
        # 计算alpha_076因子
        # 计算 DELAY(CLOSE, 1)
        df['close_delay'] = df['close'].shift(1)

        # 计算 收益率的绝对值 / 成交量
        df['abs_return_vol'] = abs(
            (df['close'] / df['close_delay'] - 1)) / df['volume']

        # 计算std和mean
        df['std_20'] = df['abs_return_vol'].rolling(window=20).std()
        df['mean_20'] = df['abs_return_vol'].rolling(window=20).mean()

        # 最终计算因子
        df['alpha_076'] = df['std_20'] / df['mean_20']

        #############################################
        # 计算alpha_079因子
        df['pre_close'] = df['close'].shift(1)
        df['delta'] = df['close'] - df['pre_close']
        df['max_delta'] = np.maximum(df['delta'], 0)
        df['abs_delta'] = abs(df['delta'])
        df['sma_max'] = df['max_delta'].rolling(window=12).mean()
        df['sma_abs'] = df['abs_delta'].rolling(window=12).mean()
        df['alpha_079'] = (df['sma_max'] / df['sma_abs']) * 100

        #############################################
        # 计算alpha_080因子
        df['alpha_080'] = df['volume'].ewm(alpha=2/22, adjust=False).mean()

        #############################################
        # 计算alpha_111因子
        df['M'] = df['volume'] * ((df['close'] - df['low']) -
                                  (df['high'] - df['close'])) / (df['high'] - df['low'])
        df['SMA_11'] = df['M'].ewm(alpha=2/11, adjust=False).mean()
        df['SMA_4'] = df['M'].ewm(alpha=2/4, adjust=False).mean()
        df['alpha_111'] = df['SMA_11']-df['SMA_4']
        #############################################
        # 计算alpha_127因子
        df['max_close_12'] = df['close'].rolling(window=12).max()
        df['percent_change_sq'] = (
            (100 * (df['close'] - df['max_close_12']) / df['max_close_12']) ** 2)
        df['mean_sq'] = df['percent_change_sq'].rolling(window=12).mean()
        df['alpha_127'] = df['mean_sq'].pow(0.5)

        #############################################
        # 计算alpha_150因子
        df['alpha_150'] = df['typical_price']*df['volume']

        #############################################
        # 计算alpha_155因子
        alpha_13_2 = 2 / (13 + 1)
        alpha_27_2 = 2 / (27 + 1)
        alpha_10_2 = 2 / (10 + 1)
        df['sma_vol_13_2'] = df['volume'].ewm(alpha=2/13, adjust=False).mean()
        df['sma_vol_27_2'] = df['volume'].ewm(alpha=2/27, adjust=False).mean()
        df['diff_sma'] = df['sma_vol_13_2'] - df['sma_vol_27_2']
        df['sma_diff_10_2'] = df['diff_sma'].ewm(
            alpha=2/10, adjust=False).mean()
        df['alpha_155'] = df['diff_sma'] - df['sma_diff_10_2']

        #############################################
        # 计算alpha_165因子
        df['mean_48'] = df['close'].rolling(window=48).mean()
        df['close_diff'] = df['close'] - df['mean_48']
        df['cum_sum'] = df['close_diff'].cumsum()
        max_sumac = df['cum_sum'].max()
        min_sumac = df['cum_sum'].min()
        df['std_48'] = df['close'].rolling(window=48).std()
        df['alpha_165'] = (max_sumac - min_sumac) / df['std_48']

        #############################################
        # 计算alpha_173因子
        span = 13
        alpha = 2 / (span + 1)
        sma1 = df['close'].ewm(alpha=alpha, adjust=False).mean()
        sma2 = sma1.ewm(alpha=alpha, adjust=False).mean()
        sma3 = np.log(df['close']).ewm(alpha=alpha, adjust=False).mean()
        sma3 = sma3.ewm(alpha=alpha, adjust=False).mean()
        sma3 = sma3.ewm(alpha=alpha, adjust=False).mean()
        df['alpha_173'] = 3 * sma1 - 2 * sma2 + sma3

        #############################################
        # 计算alpha_175因子
        df['close_delay'] = df['close'].shift(1)
        df['high_low'] = df['high'] - df['low']
        df['close_high'] = abs(df['close_delay'] - df['high'])
        df['close_low'] = abs(df['close_delay'] - df['low'])
        df['max_diff'] = df[['high_low',
                             'close_high', 'close_low']].max(axis=1)
        df['alpha_175'] = df['max_diff'].rolling(window=6).mean()

        #############################################
        # 计算alpha_189因子
        mean_close_6 = df['close'].rolling(window=6).mean()
        abs_dev = (df['close'] - mean_close_6).abs()
        df['alpha_189'] = abs_dev.rolling(window=6).mean()

        #############################################
        # 计算alpha_190因子
        df['r_t'] = df['close'].pct_change()
        df['r_bar'] = (df['close'] / df['close'].shift(19)) ** (1/20) - 1
        count_up = (df['r_t'] > df['r_bar']).rolling(window=20).sum()
        count_down = (df['r_t'] < df['r_bar']).rolling(window=20).sum()
        var_up = ((df['r_t'] - df['r_bar'])**2 * (df['r_t'] >
                  df['r_bar'])).rolling(window=20).sum() / count_up
        var_down = ((df['r_t'] - df['r_bar'])**2 * (df['r_t'] <
                    df['r_bar'])).rolling(window=20).sum() / count_down
        df['alpha_190'] = np.log(
            (count_up - 1) / count_down * (var_up / var_down))

        #############################################
        # 计算alpha_191因子
        df['V_mean'] = df['volume'].rolling(window=20).mean()
        df['Corr'] = df['V_mean'].rolling(window=10).corr(df['close'])
        df['Mid_Price'] = (df['high'] + df['low']) / 2
        df['alpha_191'] = df['Corr'] + df['Mid_Price'] - df['close']

        #############################################
        # 计算ats因子
        df['ats'] = df['volume']/df['trade_count']

        #############################################
        # 计算tcv因子
        df['tcv'] = df['trade_count'].rolling(20).std()

        #############################################
        # 计算tvs因子
        df['tvs'] = df['trade_count'] * df['volume']

        #############################################
        # 计算pp因子
        df['up'] = df['close'].rolling(20).max()
        df['down'] = df['close'].rolling(20).min()
        df['pp'] = (df['close']-df['down'])/(df['up']-df['down'])
        #############################################
        # 计算kline_distance因子
        df['kline_distance'] = (
            2*(df['high'] - df['low'])-abs(df['close']-df['open']))/df['volume']
        #############################################
        # 计算upper_shadow和lower_shadow
        df['upper_shadow'] = df['high']-df[['close', 'open']].max(axis=1)
        df['lower_shadow'] = df[['close', 'open']].min(axis=1)-df['low']
        df['upper_shadow2'] = df['high']/df[['close', 'open']].max(axis=1)
        df['lower_shadow2'] = df[['close', 'open']].min(axis=1)/df['low']
        # df['upper_shadow_ln'] = np.log(df['upper_shadow2'])
        # df['lower_shadow_ln'] = np.log(df['lower_shadow2'])
        #############################################
        df['volatility15'] = df['close'].rolling(15).std(
            ddof=0) / df['close'].rolling(500).std(ddof=0)

        df['volatility200'] = df['close'].rolling(200).std(
            ddof=0) / df['close'].rolling(2000).std(ddof=0)
        #############################################
        # df['trend_angle'] = df['close'].rolling(20).apply(
        #     lambda y: np.degrees(
        #         np.arctan(np.polyfit(np.linspace(0, 1, 20), y, 1)[0])), raw=True
        # )
        #############################################
        df['natr'] = df['atr']/df['close']
        #############################################
        df['ma10'] = df['close'].rolling(10).mean()
        df['rsi_ma10'] = df['rsi'].rolling(10).mean()
        df["sigg"] = np.where(
            (df["ma10"] > df["ma10"].shift(1)) & (
                df["rsi_ma10"] < df["rsi_ma10"].shift(1)), 1,
            np.where(
                (df["ma10"] < df["ma10"].shift(1)) & (
                    df["rsi_ma10"] > df["rsi_ma10"].shift(1)), -1,
                0
            )
        )
        # print(df['sigg'].describe())
        #############################################
        df['abs_energy'] = df['close'].rolling(
            window=5).apply(lambda x: (x ** 2).sum(), raw=True)
        #############################################
        df['wudi'] = c_chu030(df)
        # # 计算 High 和 Low 的变化
        # high_diff = df['high'].diff()
        # low_diff = df['low'].diff()

        # # 计算 Plus DM 和 Minus DM
        # plus_dm = np.where((high_diff > low_diff) &
        #                    (high_diff > 0), high_diff, 0)
        # minus_dm = np.where((low_diff > high_diff) &
        #                     (low_diff > 0), low_diff, 0)

        # # 计算 Plus DI 和 Minus DI（直接使用 df['atr']）
        # df['plus_di'] = (pd.Series(plus_dm, index=df.index).rolling(
        #     14).sum().fillna(0) / df['atr'].fillna(0)).fillna(0)
        # df['minus_di'] = (pd.Series(minus_dm, index=df.index).rolling(
        #     14).sum().fillna(0) / df['atr'].fillna(0)).fillna(0)
        #############################################
        # beta = 1.2
        # gamma = 0.8
        # df['NL_Liquidity'] = (df['high'] - df['low']) ** beta * \
        #     (df['volume'] / df['turnover']) ** gamma

        # 3rd place solution
        # lag = 900
        # df['log_close'] = np.log(df['close']/df['close'].rolling(lag).mean())

        # df['log_return'] = np.log(df['close']/df['close'].shift(lag))

        # df['ma_log_close'] = df['log_close'].rolling(lag).mean()

        # df['ma_log_return'] = df['log_return'].rolling(lag).mean()

        # df['diff_log_close'] = df['log_close']-df['ma_log_close']

        # df['diff_log_return'] = df['log_return']-df['ma_log_return']
        #############################################
        # 计算momentum因子
        # window = 100
        # df['log_return'] = (df['close']-df['close'].shift(1)
        #                     ) / df['close'].shift(1)
        # df['rank'] = df['log_return'].rolling(window).rank(pct=False)
        # # 归一化排名（最小排名映射到0，最大排名映射到1）
        # df['momentum'] = (df['rank'] - df['rank'].rolling(window).min()) / (
        #     df['rank'].rolling(window).max() - df['rank'].rolling(window).min()
        # )
        #############################################
        # np.random.seed(42)  # 保证每次运行结果相同
        # df['long_signal'] = np.random.choice(
        #     [0, 1], size=len(df), p=[0.7, 0.3])  # 70% 不开仓，30% 开多仓
        #############################################
        # pattern = np.array([1, 0])  # 定义循环模式
        # df['long_signal2'] = np.tile(
        #     pattern, len(df) // len(pattern) + 1)[:len(df)]
        #############################################
        #############################################

        # 去除nan
        df = df.fillna(0)
        # 掐头去尾
        # df = df.iloc[100:-100]
        # # 傅里叶变换
        # fft_values
        # = np.fft.fft(df['HV'].dropna())
        # frequencies = np.fft.fftfreq(len(fft_values))
        # power = np.abs(fft_values)

        # # 创建新的figure
        # plt.figure()  # 启动一个新的画布
        # plt.plot(frequencies, power)
        # plt.title('Fourier Transform Spectrum')
        # plt.xlabel('Frequency')
        # plt.ylabel('Amplitude')
        # plt.show(block=False)

        # print(len(df['chao']))
        # print(len(df['return']))
        # print(df['chao'].isnull().sum())
        # print(df['return'].isnull().sum())
        # print((df['chao'] == np.inf).sum
        # (), (df['chao'] == -np.inf).sum())
        # print((df['return'] == np.inf).sum(), (df['return'] == -np.inf).sum())
        # print(df['chao'].nunique(), df['return'].nunique())

        # exit()
        # print(df.columns)
        # exit()

        # 计算 pearson 相关系数
        pearson_corr_matrix = df[['rsi', 'rsi_deviation', 'atr', 'chao1',
                                  'chao2', 'chao3', 'chao4', 'chao5', 'HV2', 'wudi', 'vwap', 'vwap_deviation', 'return']].corr(method='pearson')

        operator_corr_matrix = df[['HV', 'HV_cb', 'HV_sq', 'HV_sqrt', 'HV_diff1', 'HV_diff2',
                                   'HV_cumsum', 'HV_ln', 'HV_exp', 'HV_sigmoid', 'HV_tanh', 'return']].corr(method='pearson')

        # print(pearson_corr_matrix)

        # 计算 spearman 相关系数

        # pearson_corr_matrix = df[['rsi', 'rsi_deviation', 'atr', 'chao1',
        #                           'chao2', 'chao3', 'chao4', 'vwap', 'v
        # wap_deviation', 'vwap_cross', 'return']].corr(method='spearman')
        # print(pearson_corr_matrix)

        '''
        第一部分，统计显著性检验

        '''
        tested_factor = 'wudi'
        # # 生成季度时间区间
        # time_ranges = pd.date_range(
        #     "2022-01-01", "2024-01-01", freq="3MS").strftime("%Y-%m-%d").tolist()

        # # 设置子图行数和列数
        # num_subplots = len(time_ranges) - 1
        # rows = (num_subplots // 4) + (1 if num_subplots %
        #                               4 != 0 else 0)  # 每行 4 个，动态调整行数

        # fig, axes = plt.subplots(rows, 4, figsize=(
        #     16, 3 * rows), constrained_layout=True)  # 创建子图
        # axes = axes.flatten()  # 拉平成一维数组，方便索引

        # # 遍历时间区间，绘制直方图
        # for i in range(num_subplots):
        #     start, end = time_ranges[i], time_ranges[i + 1]
        #     # 选取时间段的数据，并去掉 NaN
        #     subset = df.loc[start:end, tested_factor].dropna()
        #     sns.histplot(subset, bins=100, ax=axes[i], alpha=0.7, kde=False)

        #     axes[i].set_title(f"{start[:7]}-{end[:7]}")
        #     axes[i].set_xlabel("factor value")
        #     axes[i].set_ylabel("freq")

        # # 隐藏多余的子图
        # for j in range(i + 1, len(axes)):
        #     fig.delaxes(axes[j])
        # plt.show(block=False)

        # exit()

        sns.histplot(df[tested_factor], bins=30,
                     kde=True)  # kde=True 显示核密度估计曲线
        plt.title(f'{tested_factor} sistribution')
        plt.show(block=False)

        r = df[tested_factor].corr(df['return'])  # 计算相关性
        n = len(df)  # 样本量

        # 计算t值
        t_stat = r * ((n - 2) ** 0.5) / ((1 - r**2) ** 0.5)

        # 计算p值
        p_value = 2 * (1 - stats.t.cdf(abs(t_stat), df=n-2))
        print('='*50)
        print('-'*20, f'{tested_factor}', '-'*20)
        print(f'----------{currency}----------')
        print(f'----------相关系数: {r:.6f}----------')
        print(f'----------t值: {t_stat:.2f}----------p值: {p_value:.6f}')
        print('='*50)

        '''
        第二部分，交叉验证
        '''
        # 直接按行索引均分为 10 份
        chunks = np.array_split(df[[tested_factor, 'return']], 10)

        # 计算每份的 pearson 相关系数
        pearson_corrs = [chunk[[tested_factor, 'return']].corr().iloc[0, 1]
                         for chunk in chunks]

        # 输出相关系数列表
        print('每组数据的相关系数:')
        for corr in pearson_corrs:
            print(corr)
        print('='*50)
        print('IR值:', np.mean(pearson_corrs)/np.std(pearson_corrs))

        # 头尾部超额
        tail_stats, head_stats = analyze_extreme_values(
            df, col=tested_factor)

        print('='*50)
        print("尾部return数据统计:")
        # print(tail_stats['return'])
        print(f'简单夏普: {tail_stats["return"]
              ["mean"]/tail_stats["return"]["std"]}')
        print(tail_stats['return']
              ['count']*tail_stats['return']['mean'])

        print('='*50)
        print("头部return数据统计:")
        # print(head_stats['return'])
        print(f'简单夏普: {head_stats["return"]
              ["mean"]/head_stats["return"]["std"]}')
        print(head_stats['return']
              ['count']*head_stats['return']['mean'])

        print('='*50)
        # 计算并绘制因子分组的均值条形图
        calculate_and_plot_group_means(
            df, factor_col=tested_factor, return_col='return', n_groups=20)

        # IR = (head_stats['return']['mean'] -
        #       tail_stats['return']['mean']) / df['return'].std()
        # print(f"信息比率（IR）: {IR:.4f}")

        # exit()

        # 画折线图
        plt.figure(figsize=(8, 5))
        plt.plot(range(1, 11), pearson_corrs, marker='o',
                 linestyle='-', color='b', label='Pearson Correlation')

        # 添加标题和坐标轴标签
        plt.title(f'{currency} pearson Correlation')
        plt.xlabel('Chunk Index')
        plt.ylabel('Correlation Coefficient')
        plt.xticks(range(1, 11))  # 设置 x 轴刻度
        plt.axhline(y=0, color='gray', linestyle='--', linewidth=1)  # 添加零轴参考线
        plt.legend()
        plt.grid(True)

        # 显示图表
        plt.show(block=False)

        # 头尾部超额

        '''
        第三部分，经济学检验,见mark
        '''

        '''
        第四部分，可行性检验(待完成)
        '''

        # print((df['rsi_deviation'] != 0).sum())
        # print(len(df['rsi_deviation']))

        # #############################################
        # 画pearson 相关系数热力图
        plt.figure(figsize=(14, 12))
        sns.heatmap(pearson_corr_matrix, annot=True, annot_kws={'fontsize': 10},
                    cmap='coolwarm', fmt=".5f", linewidths=0.5)
        plt.title("Heatmap of Pearson Correlation Matrix",
                  fontsize=20, fontweight='bold')
        plt.xticks(fontsize=14, rotation=45)
        plt.yticks(fontsize=14, rotation=0)
        plt.show(block=False)

        import matplotlib.pyplot as plt
        import numpy as np
        from scipy.stats import linregress

        # 拟合一条线性回归线
        x = df[tested_factor]
        y = df['return']
        slope, intercept, r_value, p_value, std_err = linregress(x, y)
        line = slope * x + intercept

        # 散点图
        plt.figure(figsize=(10, 6))
        plt.scatter(x, y, s=1, color='blue', alpha=0.6, label='Data')
        plt.plot(x, line, color='red', linewidth=1.5,
                 label=f'Regression Line (r={r_value:.2f})')
        plt.xlabel(tested_factor)
        plt.ylabel('Return')
        plt.title('Scatter Plot with Linear Regression')
        plt.grid(True)
        plt.legend()
        plt.show(block=True)

        # print('-----------------------------------------------------------')
        # print(df[(df[tested_factor] > 1) & (
        #     df[tested_factor] < 1.001)]['return'].describe())
        # print(df[(df[tested_factor] > 1.001) & (
        #     df[tested_factor] < 1.002)]['return'].describe())
        # print(df[(df[tested_factor] > 1.002) & (
        #     df[tested_factor] < 1.003)]['return'].describe())
        # print(df[(df[tested_factor] > 1.003) & (
        #     df[tested_factor] < 1.004)]['return'].describe())
        # print(df[(df[tested_factor] > 1.004) & (
        #     df[tested_factor] < 1.005)]['return'].describe())
        # print(df[(df[tested_factor] > 1.005) & (
        #     df[tested_factor] < 1.01)]['return'].describe())
        # print(df[(df[tested_factor] > 1.01)]['return'].describe())
        # print('-----------------------------------------------------------')
        # exit()
        # #############################################

        # 画spearman 相关系数热力图
        # plt.figure(figsize=(12, 10))
        # sns.heatmap(spearman_corr_matrix, annot=True,
        #             cmap='coolwarm', fmt=".6f", linewidths=0.5)
        # plt.title("Heatmap of Spearman Correlation Matrix")
        # plt.show()
        # exit()

        # corr = np.corrcoef(df['return'], df['rsi_deviation'])[0, 1]
        # print('-'*50)
        # print(
        #     f'第{t-9}次循环，相关系数为：{corr}{"          corr绝对值大于0.01" if abs(corr) > 0.01 else ""}')

        # print(df[['rsi', 'atr', 'return']])
