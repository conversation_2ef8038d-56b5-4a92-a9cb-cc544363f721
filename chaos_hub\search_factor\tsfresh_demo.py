import pandas as pd
import numpy as np
import datetime
from tsfresh import extract_features
from tsfresh.utilities.dataframe_functions import roll_time_series


if __name__ == '__main__':

    # 生成示例数据
    np.random.seed(42)
    dates = pd.date_range(start='2023-01-01', periods=30)
    codes = ['A', 'B']

    data = pd.DataFrame({
        'date': np.tile(dates, len(codes)),
        'code': np.repeat(codes, len(dates)),
        'value': np.random.randn(len(codes)*len(dates)) * 100
    })

    # 添加第二列特征
    data['value2'] = data['value'] * 0.5 + np.random.randn(len(data)) * 20

    print("原始数据示例：")
    print(data.head())

    # 记录开始时间
    start_time = datetime.datetime.now()

    # 创建滚动窗口序列（每个序列长度5-20天）
    data_roll = roll_time_series(
        data,
        column_id='code',
        column_sort='date',
        max_timeshift=20,
        min_timeshift=5
    ).drop(columns=['code'])

    # 特征提取
    data_feat = extract_features(
        data_roll,
        column_id='id',
        column_sort='date',
        # n_jobs=0  # 禁用并行计算（简单示例）
    )

    # 记录结束时间
    end_time = datetime.datetime.now()

    # 输出信息
    print('\n开始时间:', start_time.strftime('%Y-%m-%d %H:%M:%S'))
    print('结束时间:', end_time.strftime('%Y-%m-%d %H:%M:%S'))
    print('耗时: %d 秒' % (end_time - start_time).seconds)
    print('原始数据维度:', data.shape)
    print('特征提取后维度:', data_feat.shape)
    print('\n提取的特征示例:')
    print(data_feat.iloc[:2, :5])  # 展示前2个样本的前5个特征
