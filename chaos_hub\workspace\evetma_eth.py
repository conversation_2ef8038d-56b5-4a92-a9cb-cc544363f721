
import datetime as dt
import os
import pandas as pd
import pickle
import numpy as np
from scipy.signal import find_peaks
import matplotlib.pyplot as plt
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from sqlalchemy import create_engine

MYSQL_CONFIG = {
    "host": "************",
    "user": "root",
    "password": "1234",
    "database": "ctadata"
}


class DataManager():

    def __init__(self):
        # self.__tim__ = ticker.TickerInfoManager()
        self.problem_ticker = []
        self.engine = create_engine(f"mysql+pymysql://{MYSQL_CONFIG['user']}:{
                                    MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}/{MYSQL_CONFIG['database']}")
        return

    def concat_data(self, data):
        """读取并合并单品类永续合约的K线数据"""
        query = f"SELECT * FROM {data.contract}"
        df = pd.read_sql(query, self.engine)
        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
        df.set_index('open_time', inplace=True)
        # 将所有K线数据合并
        data.data = df
        return


class Data():

    def __init__(self, contract):
        self.contract = contract
        return


class EventAnal():

    def __init__(self):
        return

    def get_data(self, d):
        """获取最新的5000条K线数据"""
        td = d.data
        return td.iloc[-5000:].reset_index(drop=True)

    def find_ticker_event(self, d, sig, flt, sign, is_absolute=False):
        """查找事件并计算收益"""
        td = d.data
        s = sig(td)
        fs = flt(td)
        if sign != 0:
            el = s[s == sign]
        else:
            el = s[abs(s) == 1]

        ret = {}
        fret = {}
        for i in el.index:
            eloc = td.index.get_loc(i)
            e = (td.close.iloc[-1201 + eloc:1200 + eloc] /
                 td.close.iloc[eloc] - 1) * el[i]
            e.index = range(-1200, len(e) - 1200)
            ret[i] = e
            if is_absolute:
                fret[i] = fs.loc[i]
            else:
                fret[i] = fs.loc[i] * el[i]
        ret = pd.DataFrame(ret)
        fret = pd.Series(fret)
        return ret, fret

    def find_event(self, d, sig, flt, sign, is_absolute=False):
        ret, fret = self.find_ticker_event(d, sig, flt, sign, is_absolute)
        return ret, fret

    def plot_event(self, d, sig):
        td = d.data
        s = sig(td)
        s1 = td.close.reset_index(drop=True)
        s2 = td.close.where((s == 1), np.nan).reset_index(
            drop=True).plot(marker='^')
        s3 = td.close.where(
            (s == -1), np.nan).reset_index(drop=True).plot(marker='v')
        s1.plot(color='black', alpha=0.5)
        s2.plot(color='red')
        s3.plot(color='blue')
        return


def ret_ma_cross_sig_price(d):
    c = d.close
    long_T = 30
    short_T = 5

    ma_short = c.rolling(window=short_T).mean()
    ma_long = c.rolling(window=long_T).mean()

    high_short = c.rolling(window=short_T).max()
    high_long = c.rolling(window=long_T).max()
    low_short = c.rolling(window=short_T).min()
    low_long = c.rolling(window=long_T).min()

    vol_short = c.rolling(window=short_T).sum()
    vol_long = c.rolling(window=long_T).sum()

    condu = ((((ma_short > ma_long).astype(int).diff() == 1).astype(
        int) + (high_short >= high_long).astype(int)) == 2).astype(int)
    condd = ((((ma_short < ma_long).astype(int).diff() == 1).astype(
        int) + (low_short <= low_long).astype(int)) == 2).astype(int)

    ft = vol_short / vol_long

    s = condu - condd

    return s


def F_bandportion(d):
    c = d.close
    long_T = 30
    short_T = 5
    ma_short = c.rolling(window=short_T).mean()
    ma_long = c.rolling(window=long_T).mean()

    high_short = c.rolling(window=short_T).max()
    high_long = c.rolling(window=long_T).max()
    low_short = c.rolling(window=short_T).min()
    low_long = c.rolling(window=long_T).min()

    vol_short = d.volume.rolling(window=short_T).sum()
    vol_long = d.volume.rolling(window=long_T).sum()

    s = ma_short / ma_long

    return s


if __name__ == '__main__':
    d = Data('ETHUSDT_2024')
    dm = DataManager()
    dm.concat_data(d)
    ea = EventAnal()
    td = ea.get_data(d)
    el, ft = ea.find_ticker_event(
        d, ret_ma_cross_sig_price, F_bandportion, 0, True)
    qc = pd.qcut(ft.fillna(0), [0, 0.2, 0.4, 0.6, 0.8, 1], labels=False)
    rs = el.T.groupby(qc).mean().T
    # rs_mean = el.T.mean().T

    ea.plot_event(d, ret_ma_cross_sig_price)
    rs.plot()
    el.mean(axis=1).plot(color='black')

    plt.show()
    print('done')

    # # 创建包含两个子图的图表

    # # 把rs的图和el.mean(axis=1).plot(color='black')的图显示在plotly上
    # fig = go.Figure()
    # for i in range(5):
    #     fig.add_trace(go.Scatter(x=rs.index, y=rs[i], mode='lines', name=f'{i}'))
    # # fig.add_trace(go.Scatter(x=rs_mean.index, y=rs_mean, mode='lines', name='mean', line=dict(color='black')))
    # fig.add_trace(go.Scatter(x=el.mean(axis=1).index, y=el.mean(axis=1), mode='lines', name='mean', line=dict(color='black')))
    # fig.update_layout(
    #     title='Event Signal',
    #     xaxis_title='Time',
    #     yaxis_title='Price',
    #     dragmode='zoom',
    #     xaxis=dict(rangeslider=dict(visible=True)),
    #     yaxis=dict(autorange=True),  # 添加这一行
    #     hovermode='x unified')
    # fig.show()
    # print('done')

    # # 把ea.plot_event(d, ret_ma_cross_sig_price)的图显示在plotly上
    # fig = go.Figure()
    # # td = d.data[-5000:]
    # s = ret_ma_cross_sig_price(td)
    # # fig.add_trace(go.Scatter(x=td.index, y=td.close, mode='lines', name='close'))
    # s1 = td.close.reset_index(drop=True)
    # s2 = td.close.where((s == 1), np.nan).reset_index(drop=True)
    # s3 = td.close.where((s == -1), np.nan).reset_index(drop=True)
    # fig.add_trace(
    #         go.Candlestick(
    #         x=s1.index,
    #         open=td['open'],
    #         high=td['high'],
    #         low=td['low'],
    #         close=td['close'],
    #         name='K线图'
    #     )
    # )
    # # fig.add_trace(go.Scatter(x=s1.index, y=s1, mode='lines', name='close'))
    # fig.add_trace(go.Scatter(x=s1.index, y=s2, mode='markers', name='buy', marker=dict(color='red', symbol='triangle-up', size=10)))
    # fig.add_trace(go.Scatter(x=s1.index, y=s3, mode='markers', name='sell', marker=dict(color='blue', symbol='triangle-down', size=10)))
    # fig.update_layout(
    #     title='Event Signal',
    #     xaxis_title='Time',
    #     yaxis_title='Price',
    #     dragmode='zoom',
    #     xaxis=dict(rangeslider=dict(visible=True)),
    #     yaxis=dict(autorange=True)  # 添加这一行
    #     )
    # fig.show()
    # print('done')
