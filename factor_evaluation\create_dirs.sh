#!/bin/bash

# create_dirs.sh - 以backtest_writer用户身份创建目录
# 用法: bash create_dirs.sh <目录路径1> [目录路径2] [目录路径3] ...

# 检查参数数量
if [ $# -eq 0 ]; then
    echo "错误: 至少需要一个目录路径参数"
    echo "用法: $0 <目录路径1> [目录路径2] [目录路径3] ..."
    exit 1
fi

# 显示当前执行用户信息
echo "🔧 Shell脚本调试信息:"
echo "   当前执行用户: $(whoami)"
echo "   用户ID: $(id -u)"
echo "   用户组: $(id -g)"

# 创建所有指定的目录
SUCCESS_COUNT=0
TOTAL_COUNT=$#

for DIR_PATH in "$@"; do
    echo "   正在创建目录: $DIR_PATH"
    
    # 创建目录（包括父目录）
    mkdir -p "$DIR_PATH"
    
    # 检查创建是否成功
    if [ $? -eq 0 ]; then
        if [ -d "$DIR_PATH" ]; then
            echo "   ✅ 目录创建成功: $DIR_PATH"
            echo "   📁 目录所有者: $(stat -c '%U' "$DIR_PATH" 2>/dev/null || echo 'unknown')"
            echo "   📁 目录权限: $(stat -c '%a' "$DIR_PATH" 2>/dev/null || echo 'unknown')"
            SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        else
            echo "   ❌ 目录创建失败: $DIR_PATH (目录不存在)"
        fi
    else
        echo "   ❌ 目录创建失败: $DIR_PATH (mkdir命令失败)"
    fi
done

echo "📊 创建结果: $SUCCESS_COUNT/$TOTAL_COUNT 个目录创建成功"

# 如果所有目录都创建成功，返回0；否则返回1
if [ $SUCCESS_COUNT -eq $TOTAL_COUNT ]; then
    exit 0
else
    exit 1
fi
