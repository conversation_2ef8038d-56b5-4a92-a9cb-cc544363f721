"""
单均线阈值策略
策略逻辑：
1. 只有一条长期均线，当连续两根K线的收盘价上穿均线+阈值时，视为做多信号，反之为做空信号
2. 平多条件：
   - 当前持有多单，但是出现做空信号时，直接平多，并且开空单
   - 当前持有多单，但是当前（收盘价-长期均线）>=3*（过去200个窗口内的最高价-过去200个窗口内的最低价），这时平价格为30的仓位
   - 当前持有多单，但是当前（收盘价-长期均线）>=5*（过去200个窗口内的最高价-过去200个窗口内的最低价），这时平价格为40的仓位
   - 已经平了两次仓位且中间不出现反向信号的情况下，继续持有800根K线，到期后平价格为30的仓位
3. 平空逻辑相反
"""

from typing import List
import numpy as np
from datetime import datetime
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent .parent 
sys.path.append(str(project_root))
from vnpy_backtester.templates.template import StrategyTemplate
from vnpy_backtester.objects.object import BarData
from vnpy_backtester.utils.log_engine import log_engine
from vnpy_backtester.utils.array_manager import ArrayManager


class Position:
    """
    持仓类 - 用于记录每个持仓的信息
    """

    def __init__(self, direction: str, price: float, volume: float, entry_time: datetime):
        """
        初始化持仓

        参数:
        direction: 方向，"long"或"short"
        price: 开仓价格
        volume: 开仓数量
        entry_time: 开仓时间
        """
        self.direction = direction  # "long"或"short"
        self.price = price  # 开仓价格
        self.volume = volume  # 开仓数量
        self.entry_time = entry_time  # 开仓时间
        self.bars_held = 0  # 持仓K线数量


class SingleMAThresholdStrategy(StrategyTemplate):
    """
    单均线阈值策略

    策略逻辑：
    1. 只有一条长期均线，当连续两根K线的收盘价上穿均线+阈值时，视为做多信号，反之为做空信号
    2. 复杂的平仓逻辑，包括反向信号平仓、利润平仓和时间平仓
    """

    # 策略参数
    long_window = 20  # 长期均线窗口
    threshold = 0.0  # 阈值，默认为0
    position_size = 5000  # 每次开仓的金额，默认为5000
    size = 1  # 合约乘数
    commission_rate = 0.0003  # 默认手续费率，将从引擎获取
    slippage_rate = 0.001  # 默认滑点率，将从引擎获取
    order_type = "amount"  # 下单方式，可选"quantity"(按币数量)或"amount"(按金额)

    # 平仓相关参数
    range_window = 200  # 计算价格范围的窗口，默认200
    profit_close_volume_1 = 1500  # 第一次利润平仓的金额
    profit_close_volume_2 = 2000  # 第二次利润平仓的金额
    profit_multiplier_1 = 3  # 第一次利润平仓的倍数
    profit_multiplier_2 = 5  # 第二次利润平仓的倍数
    time_close_bars = 800  # 时间平仓的K线数量
    time_close_volume = 1500  # 时间平仓的金额

    # 策略变量
    long_ma = 0.0
    positions = []  # 持仓列表，每个元素是一个Position对象
    current_pos_direction = None  # 当前持仓方向："long", "short", None
    
    # 记录历史K线数据，用于判断连续两根K线的信号
    last_close = 0.0
    last_long_ma = 0.0
    last_last_close = 0.0  # t-2时刻的收盘价
    last_last_long_ma = 0.0  # t-2时刻的均线值
    
    # 平仓记录
    profit_close_count = 0  # 利润平仓次数
    last_signal_direction = None  # 最后一次信号方向
    
    # 缓存变量
    _used_capital = 0.0  # 已使用的资金
    _last_bar_datetime = None  # 上一根K线的时间
    _cached_available_capital = None  # 缓存的可用资金

    def __init__(self, engine, strategy_name: str, vt_symbol: str, setting: dict = None):
        """
        初始化策略
        """
        super().__init__(engine, strategy_name, vt_symbol, setting)

        # 从设置中获取参数
        if setting:
            for key, value in setting.items():
                if hasattr(self, key):
                    setattr(self, key, value)

        # 记录下单方式和仓位大小
        if self.order_type == "quantity":
            self.write_log(f"设置每次开仓数量: {self.position_size}")
        else:  # order_type == "amount"
            self.write_log(f"设置每次开仓金额: {self.position_size}")

        # 从引擎获取手续费率和滑点率
        if hasattr(self, "engine"):
            self.commission_rate = self.engine.rate
            self.slippage_rate = self.engine.slippage
            self.size = self.engine.size
            self.write_log(
                f"从引擎获取参数 - 手续费率: {self.commission_rate}, 滑点率: {self.slippage_rate}, 合约乘数: {self.size}")

        # 创建K线管理器，需要足够大的窗口来计算长期均线和价格范围
        required_size = max(self.long_window, self.range_window)
        self.am = ArrayManager(size=max(int(required_size * 1.5), 300))

    def on_init(self):
        """
        策略初始化
        """
        self.write_log("单均线阈值策略初始化")

        # 从引擎获取手续费率和滑点率
        if hasattr(self, "engine"):
            self.commission_rate = self.engine.rate
            self.slippage_rate = self.engine.slippage
            self.size = self.engine.size
            self.write_log(
                f"从引擎获取参数 - 手续费率: {self.commission_rate}, 滑点率: {self.slippage_rate}, 合约乘数: {self.size}")

    def on_start(self):
        """
        策略启动
        """
        self.write_log("单均线阈值策略启动")

    def on_stop(self):
        """
        策略停止
        """
        self.write_log("单均线阈值策略停止")

    def get_available_capital(self, force_recalculate=False):
        """
        计算可用资金
        """
        # 如果缓存有效且不强制重新计算，直接返回缓存值
        if (not force_recalculate and 
            self._last_bar_datetime == self.bar.datetime and 
            self._cached_available_capital is not None):
            return self._cached_available_capital

        # 获取初始资金
        initial_capital = self.engine.capital

        # 计算已使用资金
        self._used_capital = 0.0
        for position in self.positions:
            self._used_capital += position.price * position.volume

        # 计算可用资金
        available_capital = initial_capital - self._used_capital

        # 确保可用资金不小于0
        available_capital = max(0.0, available_capital)

        # 缓存计算结果
        self._last_bar_datetime = self.bar.datetime
        self._cached_available_capital = available_capital

        return available_capital

    def on_bar(self, bar: BarData):
        """
        K线更新回调
        """
        # 保存当前K线对象，供其他方法使用
        self.bar = bar

        # 更新K线管理器
        self.am.update_bar(bar.open_price, bar.high_price,
                           bar.low_price, bar.close_price, bar.volume)

        # 如果数据不足，则不进行交易
        if not self.am.inited:
            self.write_log(
                f"数据不足，无法计算均线：{bar.datetime}, 数据点数量：{self.am.count}/{self.long_window}", level="DEBUG")
            return

        # 更新持仓的K线计数
        for position in self.positions:
            position.bars_held += 1

        # 计算长期均线
        long_ma_array = self.am.get_ma(self.long_window)
        self.long_ma = long_ma_array[-1]

        # 计算价格范围（过去200个窗口内的最高价-最低价）
        price_range = self.calculate_price_range()

        # 判断信号
        long_signal, short_signal = self.check_signals(bar)

        # 调试信息（可选）
        if hasattr(self, 'debug_mode') and self.debug_mode:
            if long_signal or short_signal:
                self.write_log(f"信号检测 - 时间: {bar.datetime}, 收盘价: {bar.close_price:.2f}, "
                             f"均线: {self.long_ma:.2f}, 做多信号: {long_signal}, 做空信号: {short_signal}")

        # 处理平仓逻辑
        self.handle_close_positions(bar, price_range)

        # 处理开仓信号
        if long_signal:
            self.handle_long_signal(bar)
        elif short_signal:
            self.handle_short_signal(bar)

        # 更新历史K线数据
        self.last_last_close = self.last_close
        self.last_last_long_ma = self.last_long_ma
        self.last_close = bar.close_price
        self.last_long_ma = self.long_ma

    def calculate_price_range(self):
        """
        计算过去200个窗口内的价格范围（最高价-最低价）
        """
        if self.am.count < self.range_window:
            # 数据不足时，使用所有可用数据
            high_array = self.am._get_latest_array(self.am.high_array)
            low_array = self.am._get_latest_array(self.am.low_array)
        else:
            # 获取最近200个数据点
            high_array = self.am._get_latest_array(self.am.high_array)[-self.range_window:]
            low_array = self.am._get_latest_array(self.am.low_array)[-self.range_window:]

        if len(high_array) > 0 and len(low_array) > 0:
            highest = np.max(high_array)
            lowest = np.min(low_array)
            return highest - lowest
        else:
            return 0.0

    def check_signals(self, bar):
        """
        检查做多和做空信号
        做多信号：t-2时刻收盘价 < 长期均线，t-1时刻收盘价 > 长期均线+阈值，t时刻收盘价 > 长期均线+阈值
        做空信号：t-2时刻收盘价 > 长期均线，t-1时刻收盘价 < 长期均线-阈值，t时刻收盘价 < 长期均线-阈值
        """
        long_signal = False
        short_signal = False

        # 需要有足够的历史数据才能判断信号
        if (hasattr(self, 'last_last_close') and self.last_last_close > 0 and
            hasattr(self, 'last_last_long_ma') and self.last_last_long_ma > 0 and
            self.last_close > 0 and self.last_long_ma > 0):

            # 做多信号：
            # t-2时刻：收盘价 < 长期均线
            # t-1时刻：收盘价 > 长期均线+阈值
            # t时刻：收盘价 > 长期均线+阈值
            if (self.last_last_close < self.last_last_long_ma and
                self.last_close > self.last_long_ma + self.threshold and
                bar.close_price > self.long_ma + self.threshold):
                long_signal = True
                self.last_signal_direction = "long"

            # 做空信号：
            # t-2时刻：收盘价 > 长期均线
            # t-1时刻：收盘价 < 长期均线-阈值
            # t时刻：收盘价 < 长期均线-阈值
            if (self.last_last_close > self.last_last_long_ma and
                self.last_close < self.last_long_ma - self.threshold and
                bar.close_price < self.long_ma - self.threshold):
                short_signal = True
                self.last_signal_direction = "short"

        return long_signal, short_signal

    def handle_close_positions(self, bar, price_range):
        """
        处理平仓逻辑
        """
        if not self.positions:
            return

        # 检查反向信号平仓
        long_signal, short_signal = self.check_signals(bar)

        # 如果当前持有多单但出现做空信号，平多开空
        if self.current_pos_direction == "long" and short_signal:
            self.close_all_positions(bar, "反向信号")
            self.profit_close_count = 0  # 重置利润平仓计数
            return

        # 如果当前持有空单但出现做多信号，平空开多
        if self.current_pos_direction == "short" and long_signal:
            self.close_all_positions(bar, "反向信号")
            self.profit_close_count = 0  # 重置利润平仓计数
            return

        # 检查利润平仓条件
        if price_range > 0:
            self.check_profit_close(bar, price_range)

        # 检查时间平仓条件
        self.check_time_close(bar)

    def check_profit_close(self, bar, price_range):
        """
        检查利润平仓条件
        """
        if self.current_pos_direction == "long":
            # 多单利润平仓
            profit_diff = bar.close_price - self.long_ma

            # 第一次利润平仓：（收盘价-长期均线）>=3*价格范围
            if (self.profit_close_count == 0 and
                profit_diff >= self.profit_multiplier_1 * price_range):
                self.close_partial_position(bar, self.profit_close_volume_1, "第一次利润平仓")
                self.profit_close_count = 1

            # 第二次利润平仓：（收盘价-长期均线）>=5*价格范围
            elif (self.profit_close_count == 1 and
                  profit_diff >= self.profit_multiplier_2 * price_range):
                self.close_partial_position(bar, self.profit_close_volume_2, "第二次利润平仓")
                self.profit_close_count = 2

        elif self.current_pos_direction == "short":
            # 空单利润平仓（逻辑相反）
            profit_diff = self.long_ma - bar.close_price

            # 第一次利润平仓：（长期均线-收盘价）>=3*价格范围
            if (self.profit_close_count == 0 and
                profit_diff >= self.profit_multiplier_1 * price_range):
                self.close_partial_position(bar, self.profit_close_volume_1, "第一次利润平仓")
                self.profit_close_count = 1

            # 第二次利润平仓：（长期均线-收盘价）>=5*价格范围
            elif (self.profit_close_count == 1 and
                  profit_diff >= self.profit_multiplier_2 * price_range):
                self.close_partial_position(bar, self.profit_close_volume_2, "第二次利润平仓")
                self.profit_close_count = 2

    def check_time_close(self, bar):
        """
        检查时间平仓条件
        已经平了两次仓位且中间不出现反向信号的情况下，继续持有800根K线，到期后平价格为30的仓位
        """
        if self.profit_close_count >= 2:
            # 检查是否有持仓超过800根K线
            for position in self.positions[:]:  # 使用切片复制列表，避免在迭代中修改列表
                if position.bars_held >= self.time_close_bars:
                    # 平掉指定数量的仓位
                    close_volume = min(position.volume, self.time_close_volume)
                    self.close_partial_position(bar, close_volume, "时间平仓")
                    break  # 一次只平一个仓位

    def close_all_positions(self, bar, reason=""):
        """
        平掉所有持仓
        """
        if not self.positions:
            return

        total_volume = sum(p.volume for p in self.positions)

        if self.current_pos_direction == "long":
            self.sell(bar.close_price, total_volume)
            self.write_log(f"平多仓 - 数量: {total_volume}, 价格: {bar.close_price:.4f}, 原因: {reason}")
        elif self.current_pos_direction == "short":
            self.cover(bar.close_price, total_volume)
            self.write_log(f"平空仓 - 数量: {total_volume}, 价格: {bar.close_price:.4f}, 原因: {reason}")

        # 清空持仓列表
        self.positions.clear()
        self.current_pos_direction = None

        # 强制重新计算可用资金
        self.get_available_capital(force_recalculate=True)

    def close_partial_position(self, bar, close_amount, reason=""):
        """
        部分平仓 - 支持按金额或按数量平仓
        """
        if not self.positions or close_amount <= 0:
            return

        # 根据order_type决定是按金额还是按数量平仓
        if self.order_type == "amount":
            # 按金额平仓：将金额转换为数量
            close_volume = close_amount / bar.close_price
        else:
            # 按数量平仓
            close_volume = close_amount

        remaining_volume = close_volume
        positions_to_remove = []
        total_closed_volume = 0

        # 从最早的持仓开始平仓
        for i, position in enumerate(self.positions):
            if remaining_volume <= 0:
                break

            if position.volume <= remaining_volume:
                # 完全平掉这个持仓
                volume_to_close = position.volume
                remaining_volume -= volume_to_close
                positions_to_remove.append(i)
            else:
                # 部分平掉这个持仓
                volume_to_close = remaining_volume
                position.volume -= volume_to_close
                remaining_volume = 0

            total_closed_volume += volume_to_close

            # 执行平仓操作
            if self.current_pos_direction == "long":
                self.sell(bar.close_price, volume_to_close)
            elif self.current_pos_direction == "short":
                self.cover(bar.close_price, volume_to_close)

        # 移除已完全平仓的持仓记录（从后往前移除，避免索引问题）
        for i in reversed(positions_to_remove):
            self.positions.pop(i)

        # 如果所有持仓都被平掉，重置状态
        if not self.positions:
            self.current_pos_direction = None

        # 记录日志
        if self.order_type == "amount":
            actual_amount = total_closed_volume * bar.close_price
            self.write_log(f"部分平仓 - 金额: {actual_amount:.2f}, 数量: {total_closed_volume:.4f}, 价格: {bar.close_price:.4f}, 原因: {reason}")
        else:
            self.write_log(f"部分平仓 - 数量: {total_closed_volume:.4f}, 价格: {bar.close_price:.4f}, 原因: {reason}")

        # 强制重新计算可用资金
        self.get_available_capital(force_recalculate=True)

    def handle_long_signal(self, bar):
        """
        处理做多信号
        """
        # 如果当前持有空仓，先平仓
        if self.current_pos_direction == "short":
            self.close_all_positions(bar, "反向信号平仓")
            self.profit_close_count = 0  # 重置利润平仓计数

        # 计算开仓数量
        if self.order_type == "quantity":
            volume = self.position_size
        else:  # order_type == "amount"
            volume = self.position_size / bar.close_price

        # 检查可用资金
        available_capital = self.get_available_capital()
        total_cost = bar.close_price * volume

        if available_capital >= total_cost:
            # 开多仓
            self.buy(bar.close_price, volume)

            # 创建新的持仓对象并添加到持仓列表
            new_position = Position("long", bar.close_price, volume, bar.datetime)
            self.positions.append(new_position)
            self.current_pos_direction = "long"

            self.write_log(f"开多仓 - 数量: {volume}, 价格: {bar.close_price:.4f}")

            # 强制重新计算可用资金
            self.get_available_capital(force_recalculate=True)
        else:
            self.write_log(f"资金不足，无法开多仓 - 需要: {total_cost:.2f}, 可用: {available_capital:.2f}")

    def handle_short_signal(self, bar):
        """
        处理做空信号
        """
        # 如果当前持有多仓，先平仓
        if self.current_pos_direction == "long":
            self.close_all_positions(bar, "反向信号平仓")
            self.profit_close_count = 0  # 重置利润平仓计数

        # 计算开仓数量
        if self.order_type == "quantity":
            volume = self.position_size
        else:  # order_type == "amount"
            volume = self.position_size / bar.close_price

        # 检查可用资金
        available_capital = self.get_available_capital()
        total_cost = bar.close_price * volume

        if available_capital >= total_cost:
            # 开空仓
            self.short(bar.close_price, volume)

            # 创建新的持仓对象并添加到持仓列表
            new_position = Position("short", bar.close_price, volume, bar.datetime)
            self.positions.append(new_position)
            self.current_pos_direction = "short"

            self.write_log(f"开空仓 - 数量: {volume}, 价格: {bar.close_price:.4f}")

            # 强制重新计算可用资金
            self.get_available_capital(force_recalculate=True)
        else:
            self.write_log(f"资金不足，无法开空仓 - 需要: {total_cost:.2f}, 可用: {available_capital:.2f}")

    def on_trade(self, trade):
        """
        交易回调
        """
        self.write_log(f"交易执行 - 方向: {trade.direction.value}, 数量: {trade.volume}, 价格: {trade.price}")

    def write_log(self, msg: str, level: str = "INFO"):
        """
        写入日志
        """
        if hasattr(self, 'engine') and hasattr(self.engine, 'write_log'):
            self.engine.write_log(msg, self)
        else:
            print(f"[{level}] {msg}")
