from factors_lib.version1 import VersionOne
from sqlalchemy import create_engine
import pandas as pd

MYSQL_CONFIG = {
    "host": "************",
    "user": "root",
    "password": "1234",
    "database": "ctadata"
}


class DataManager():

    def __init__(self):
        # self.__tim__ = ticker.TickerInfoManager()

        self.problem_ticker = []
        self.engine = create_engine(f"mysql+pymysql://{MYSQL_CONFIG['user']}:{
                                    MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}/{MYSQL_CONFIG['database']}")
        return

    def concat_data(self, data):
        """读取并合并单品类永续合约的K线数据"""
        query = f"SELECT * FROM {data.contract}"
        df = pd.read_sql(query, self.engine)
        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
        df.set_index('open_time', inplace=True)
        # 将所有K线数据合并
        data.data = df
        return


class Data():

    def __init__(self, contract):
        self.contract = contract
        return


if __name__ == '__main__':
    currency = 'BTCUSDT_15m_2022_2025 '
    d = Data(currency)
    dm = DataManager()
    dm.concat_data(d)
    df = d.data.copy()  # K线数据,index是open_time

    sig = VersionOne.d_lib001(df)
    print(sig)
