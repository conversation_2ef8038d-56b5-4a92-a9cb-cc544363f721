"""
测试单均线阈值策略的信号逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def test_signal_logic():
    """
    测试信号逻辑的正确性
    """
    print("=== 测试单均线阈值策略信号逻辑 ===")
    
    # 创建测试数据
    dates = [datetime(2023, 1, 1) + timedelta(minutes=15*i) for i in range(100)]
    
    # 创建一个明确的信号场景
    # 基础价格2000，均线窗口为5
    base_price = 2000
    prices = []
    
    # 前20个数据点：价格在均线附近波动，但不触发信号
    for i in range(20):
        prices.append(base_price + np.random.normal(0, 5))
    
    # 创建一个明确的做多信号场景
    # t-2: 价格低于均线
    prices.append(base_price - 10)  # 确保低于均线
    # t-1: 价格高于均线+阈值
    prices.append(base_price + 15)  # 确保高于均线+阈值
    # t: 价格高于均线+阈值
    prices.append(base_price + 20)  # 确保高于均线+阈值
    
    # 继续添加一些数据
    for i in range(77):
        prices.append(base_price + np.random.normal(0, 5))
    
    # 创建DataFrame
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        high = close + abs(np.random.normal(0, 2))
        low = close - abs(np.random.normal(0, 2))
        open_price = close + np.random.normal(0, 1)
        volume = 1000
        
        data.append({
            'datetime': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('datetime', inplace=True)
    
    print(f"创建了 {len(df)} 条测试数据")
    
    # 手动计算均线和信号
    window = 5
    threshold = 0.0
    
    df['ma'] = df['close'].rolling(window=window).mean()
    
    # 检查信号逻辑
    signals = []
    for i in range(2, len(df)):
        # 获取三个时刻的数据
        t_minus_2_close = df.iloc[i-2]['close']
        t_minus_2_ma = df.iloc[i-2]['ma']
        t_minus_1_close = df.iloc[i-1]['close']
        t_minus_1_ma = df.iloc[i-1]['ma']
        t_close = df.iloc[i]['close']
        t_ma = df.iloc[i]['ma']
        
        # 跳过均线为NaN的情况
        if pd.isna(t_minus_2_ma) or pd.isna(t_minus_1_ma) or pd.isna(t_ma):
            signals.append('None')
            continue
        
        # 做多信号检查
        long_signal = (t_minus_2_close < t_minus_2_ma and
                      t_minus_1_close > t_minus_1_ma + threshold and
                      t_close > t_ma + threshold)
        
        # 做空信号检查
        short_signal = (t_minus_2_close > t_minus_2_ma and
                       t_minus_1_close < t_minus_1_ma - threshold and
                       t_close < t_ma - threshold)
        
        if long_signal:
            signals.append('Long')
            print(f"检测到做多信号 - 时间: {df.index[i]}")
            print(f"  t-2: 收盘价={t_minus_2_close:.2f}, 均线={t_minus_2_ma:.2f}, 条件={t_minus_2_close < t_minus_2_ma}")
            print(f"  t-1: 收盘价={t_minus_1_close:.2f}, 均线+阈值={t_minus_1_ma + threshold:.2f}, 条件={t_minus_1_close > t_minus_1_ma + threshold}")
            print(f"  t:   收盘价={t_close:.2f}, 均线+阈值={t_ma + threshold:.2f}, 条件={t_close > t_ma + threshold}")
        elif short_signal:
            signals.append('Short')
            print(f"检测到做空信号 - 时间: {df.index[i]}")
            print(f"  t-2: 收盘价={t_minus_2_close:.2f}, 均线={t_minus_2_ma:.2f}, 条件={t_minus_2_close > t_minus_2_ma}")
            print(f"  t-1: 收盘价={t_minus_1_close:.2f}, 均线-阈值={t_minus_1_ma - threshold:.2f}, 条件={t_minus_1_close < t_minus_1_ma - threshold}")
            print(f"  t:   收盘价={t_close:.2f}, 均线-阈值={t_ma - threshold:.2f}, 条件={t_close < t_ma - threshold}")
        else:
            signals.append('None')
    
    # 统计信号
    long_count = signals.count('Long')
    short_count = signals.count('Short')
    
    print(f"\n=== 信号统计 ===")
    print(f"做多信号数量: {long_count}")
    print(f"做空信号数量: {short_count}")
    print(f"总信号数量: {long_count + short_count}")
    
    # 显示部分数据用于验证
    print(f"\n=== 部分数据展示 ===")
    df['signal'] = ['None', 'None'] + signals
    print(df[['close', 'ma', 'signal']].head(30))
    
    return df

if __name__ == "__main__":
    df = test_signal_logic()
    print("\n✅ 信号逻辑测试完成！")
