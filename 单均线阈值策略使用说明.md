# 单均线阈值策略使用说明

## 策略概述

单均线阈值策略（SingleMAThresholdStrategy）是一个基于单一长期均线的交易策略，具有复杂的开仓和平仓逻辑。

## 策略逻辑

### 开仓信号
1. **做多信号**：当连续两根K线的收盘价上穿均线+阈值时，视为做多信号
2. **做空信号**：当连续两根K线的收盘价下穿均线-阈值时，视为做空信号
3. **默认每次开仓数量**：100（可配置）

### 平仓逻辑

#### 平多条件
1. **反向信号平仓**：当前持有多单，但是出现做空信号时，直接平多，并且开空单
2. **第一次利润平仓**：当前（收盘价-长期均线）>=3*（过去200个窗口内的最高价-过去200个窗口内的最低价），平价格为30的仓位
3. **第二次利润平仓**：当前（收盘价-长期均线）>=5*（过去200个窗口内的最高价-过去200个窗口内的最低价），平价格为40的仓位
4. **时间平仓**：已经平了两次仓位且中间不出现反向信号的情况下，继续持有800根K线，到期后平价格为30的仓位

#### 平空条件
平空逻辑与平多逻辑相反：
1. **反向信号平仓**：当前持有空单，但是出现做多信号时，直接平空，并且开多单
2. **第一次利润平仓**：当前（长期均线-收盘价）>=3*价格范围，平30的仓位
3. **第二次利润平仓**：当前（长期均线-收盘价）>=5*价格范围，平40的仓位
4. **时间平仓**：同多单逻辑

## 策略参数

### 主要参数
- `long_window`: 长期均线窗口，默认20
- `threshold`: 阈值，默认0.0
- `position_size`: 每次开仓的数量，默认100
- `order_type`: 下单方式，"quantity"(按币数量)或"amount"(按金额)

### 平仓相关参数
- `range_window`: 计算价格范围的窗口，默认200
- `profit_close_volume_1`: 第一次利润平仓的数量，默认30
- `profit_close_volume_2`: 第二次利润平仓的数量，默认40
- `profit_multiplier_1`: 第一次利润平仓的倍数，默认3
- `profit_multiplier_2`: 第二次利润平仓的倍数，默认5
- `time_close_bars`: 时间平仓的K线数量，默认800
- `time_close_volume`: 时间平仓的数量，默认30

## 使用方法

### 1. 导入策略
```python
from vnpy_backtester.scripts.run_single_ma_threshold_strategy import run_single_ma_threshold_strategy_backtest
```

### 2. 准备数据
确保您的DataFrame包含以下列：
- `open`: 开盘价
- `high`: 最高价
- `low`: 最低价
- `close`: 收盘价
- `volume`: 成交量

索引必须是datetime类型。

### 3. 运行回测
```python
engine = run_single_ma_threshold_strategy_backtest(
    df=df,                      # 数据DataFrame
    long_window=20,             # 长期均线窗口
    threshold=0.0,              # 阈值
    position_size=100,          # 每次开仓数量
    range_window=200,           # 计算价格范围的窗口
    profit_close_volume_1=30,   # 第一次利润平仓的数量
    profit_close_volume_2=40,   # 第二次利润平仓的数量
    profit_multiplier_1=3,      # 第一次利润平仓的倍数
    profit_multiplier_2=5,      # 第二次利润平仓的倍数
    time_close_bars=800,        # 时间平仓的K线数量
    time_close_volume=30,       # 时间平仓的数量
    rate=0.0003,                # 手续费率
    slippage=0.01,              # 滑点
    capital=50000,              # 初始资金
    order_type="quantity",      # 下单方式
)
```

## 示例代码

```python
import pandas as pd
from vnpy_backtester.scripts.run_single_ma_threshold_strategy import run_single_ma_threshold_strategy_backtest

# 加载数据
df = pd.read_csv('your_data.csv')
df.index = pd.to_datetime(df.index)  # 确保索引是datetime类型

# 运行回测
engine = run_single_ma_threshold_strategy_backtest(
    df=df,
    long_window=20,
    threshold=0.0,
    position_size=100,
    rate=0.0003,
    slippage=0.01,
    capital=50000,
    order_type="quantity",
)
```

## 文件位置

- **策略文件**: `vnpy_backtester/strategies/single_ma_threshold_strategy.py`
- **运行脚本**: `vnpy_backtester/scripts/run_single_ma_threshold_strategy.py`
- **测试脚本**: `test_single_ma_strategy.py`

## 注意事项

1. 确保数据质量良好，包含完整的OHLCV数据
2. 根据您的交易品种和时间周期调整参数
3. 建议先在历史数据上进行充分的回测验证
4. 注意资金管理，避免过度杠杆
5. 策略包含复杂的平仓逻辑，建议仔细理解后使用

## 调试模式

可以通过设置 `debug=True` 来开启调试模式，查看详细的信号和交易信息：

```python
engine = run_single_ma_threshold_strategy_backtest(
    df=df,
    debug=True,  # 开启调试模式
    # 其他参数...
)
```
