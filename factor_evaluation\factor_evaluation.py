# -*- coding: utf-8 -*-
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
import requests
import inspect
import json
import os
import base64
import io
from datetime import datetime
from typing import Dict, Any, Optional, Union, Tuple
from statsmodels.tsa.stattools import adfuller
from sqlalchemy import create_engine, text


# 忽略警告以提升用户体验
warnings.filterwarnings('ignore')

# 设置pandas显示选项
pd.set_option('display.precision', 4)
pd.set_option('display.float_format', '{:.4f}'.format)


class GroupStats:
    def __init__(self, df, value_col, return_col):
        """
        初始化分析器
        :param df: 包含待分析数据的DataFrame
        :param value_col: 要分析的因子列名
        :param return_col: 目标收益率列名
        """
        self.df = df.copy()
        self.value_col = value_col
        self.return_col = return_col
        self._validate_columns()

    def _validate_columns(self):
        """验证必要列是否存在"""
        required_cols = [self.value_col, self.return_col]
        missing = [col for col in required_cols if col not in self.df.columns]
        if missing:
            raise ValueError(f"缺失必要列: {missing}")

    def analyze_groups(self, n_groups=10, show_plot=False):
        """
        综合分析方法（合并原分组和极端值分析）
        :param n_groups: 分组数量（2时等效极端值分析）
        :param show_plot: 是否显示分布图
        :return: (分组统计, 绘图对象)
        """
        # 动态分组
        self.df['group'] = pd.qcut(
            self.df[self.value_col],
            n_groups,
            labels=False,
            duplicates='drop'
        )

        # 分组统计（含极端组分析）
        stats = self.df.groupby('group').agg({
            self.value_col: ['min', 'max', 'mean'],
            self.return_col: ['mean', 'std', 'count']
        })

        # 重命名列
        stats.columns = ['val_min', 'val_max', 'val_mean',
                         'return_mean', 'return_std', 'count']

        # 绘制分布图
        fig = self._plot_distribution(n_groups, show_plot)

        return stats, fig

    def _plot_distribution(self, n_groups, show_plot):
        """绘制分组收益均值柱状图（数据分割版本）"""
        # 分割数据：最近10000个点和除最近10000个点
        recent_data, older_data = self._split_data_by_count()

        # 创建单纵坐标图
        fig, ax = plt.subplots(figsize=(15, 8))

        # 计算最近10000个点的分组收益
        if len(recent_data) > 0:
            recent_data['group'] = pd.qcut(
                recent_data[self.value_col], n_groups, labels=False, duplicates='drop'
            )
            recent_group_returns = recent_data.groupby(
                'group')[self.return_col].mean()
        else:
            recent_group_returns = pd.Series(dtype=float)

        # 计算除最近10000个点的分组收益
        if len(older_data) > 0:
            older_data['group'] = pd.qcut(
                older_data[self.value_col], n_groups, labels=False, duplicates='drop'
            )
            older_group_returns = older_data.groupby(
                'group')[self.return_col].mean()
        else:
            older_group_returns = pd.Series(dtype=float)

        # 计算实际数据量用于标签
        recent_count = len(recent_data) if len(recent_data) > 0 else 0
        older_count = len(older_data) if len(older_data) > 0 else 0

        # 格式化数据量显示
        def format_count(count):
            if count >= 1000000:
                return f"{count/1000000:.1f}M"
            elif count >= 1000:
                return f"{count/1000:.1f}K"
            else:
                return str(count)

        # 绘制最近数据的柱状图
        if len(recent_group_returns) > 0:
            bars1 = ax.bar(
                x=recent_group_returns.index - 0.2,
                height=recent_group_returns.values,
                width=0.35,
                color='lightcoral',
                edgecolor='darkred',
                linewidth=1,
                alpha=0.8,
                label=f'Recent ({format_count(recent_count)} points)'
            )

            # 添加数值标签
            for bar in bars1:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                        f'{height:.4f}',
                        ha='center', va='bottom' if height >= 0 else 'top',
                        fontsize=8, color='darkred')

        # 绘制历史数据的柱状图
        if len(older_group_returns) > 0:
            bars2 = ax.bar(
                x=older_group_returns.index + 0.2,
                height=older_group_returns.values,
                width=0.35,
                color='skyblue',
                edgecolor='navy',
                linewidth=1,
                alpha=0.8,
                label=f'Historical ({format_count(older_count)} points)'
            )

            # 添加数值标签
            for bar in bars2:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                        f'{height:.4f}',
                        ha='center', va='bottom' if height >= 0 else 'top',
                        fontsize=8, color='navy')

        # 设置图表样式
        ax.set_xlabel('Group Number', fontsize=12)
        ax.set_ylabel(f'Mean {self.return_col}', fontsize=12)

        # 设置x轴刻度
        all_groups = set()
        if len(recent_group_returns) > 0:
            all_groups.update(recent_group_returns.index)
        if len(older_group_returns) > 0:
            all_groups.update(older_group_returns.index)

        if all_groups:
            ax.set_xticks(sorted(all_groups))

        # 添加网格和图例
        ax.grid(axis='y', linestyle='--', alpha=0.3)
        ax.legend()

        plt.title(
            f'Group Return Comparison - Recent vs Historical ({n_groups} groups)', fontsize=14, pad=20)
        plt.tight_layout()

        if show_plot:
            plt.show()
        return fig

    def _split_data_by_count(self, recent_count=10000):
        """按数据点数量分割数据：最近N个点和除最近N个点"""
        total_len = len(self.df)

        if total_len <= recent_count:
            # 如果总数据量不足，全部作为最近数据
            return self.df.copy(), pd.DataFrame()

        # 分割数据
        recent_data = self.df.tail(recent_count).copy()
        older_data = self.df.head(total_len - recent_count).copy()

        return recent_data, older_data


class DataService:
    _MYSQL_CONFIG = {
        "host": "************",
        "user": "root",
        "password": "1234",
        "database": "ctadata"
    }
    _API_ENDPOINT = "http://************:6678"

    def __init__(self):
        self.engine = create_engine(
            f"mysql+pymysql://{self._MYSQL_CONFIG['user']}:{self._MYSQL_CONFIG['password']}"
            f"@{self._MYSQL_CONFIG['host']}/{self._MYSQL_CONFIG['database']}"
        )

    def __getitem__(self, key: Union[str, Tuple[str, str]]) -> Union[pd.DataFrame, pd.Series]:
        """
        双模式访问：
        - ds['表名']          → 返回K线数据
        - ds['表名', '方法名'] → 返回因子信号
        """
        if isinstance(key, str):
            return self._fetch_kline_data(key)
        elif isinstance(key, tuple) and len(key) == 2:
            table, method = key
            return self._fetch_factor_signal(table, method)
        else:
            raise KeyError("无效键格式，应为字符串或 (表名, 方法名) 元组")

    def _fetch_kline_data(self, table: str) -> pd.DataFrame:
        """获取K线数据"""
        with self.engine.connect() as connection:
            df = pd.read_sql_query(text(f"SELECT * FROM {table}"), connection)
        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
        return df.set_index('open_time').sort_index()

    def _fetch_factor_signal(self, table: str, method: str) -> pd.Series:
        """获取因子信号"""
        response = requests.get(
            self._API_ENDPOINT,
            params={"method": method, "table": table}
        )
        response.raise_for_status()

        json_data = response.json()
        series = pd.Series(json_data)
        series.index = pd.to_datetime(series.index.astype(float), unit='ms')
        return series.sort_index().rename(f'{method}_signal')


def analyze_factor_distribution(df, factor_col='signals', time_windows=10):
    """
    因子分布分析工具

    参数:
    df : 包含DatetimeIndex的DataFrame
    factor_col : 要分析的因子列名
    time_windows : int或list,分割的时间窗口数量(默认10)
    """
    # ======== 参数处理 ==========
    if isinstance(time_windows, int):
        time_windows = [time_windows]

    # ======== 基础分布特征 ==========
    desc_stats = df[factor_col].describe(
        percentiles=[.01, .05, .25, .5, .75, .95, .99])

    # ======== 极端值分析 ==========
    outlier_analysis = pd.Series({
        '>3std': (df[factor_col].abs() > 3*desc_stats['std']).mean(),
        '>5std': (df[factor_col].abs() > 5*desc_stats['std']).mean()
    })

    # ======== 时间窗口分布 ==========
    window_stats = {}

    # 获取时间范围
    min_time = df.index.min()
    max_time = df.index.max()

    for n in time_windows:
        # 生成等分时间点(增加容错处理)
        try:
            bins = pd.date_range(start=min_time, end=max_time, periods=n+1)
        except Exception as e:
            raise ValueError(f"无法分割为{n}个窗口: {str(e)}")

        # 分割时间窗口并分组统计(添加observed参数)
        intervals = pd.cut(df.index, bins=bins, right=False)

        # 使用lambda函数计算峰度(修正方法名)
        grouped = df.groupby(intervals, observed=False)[factor_col].agg(
            ['mean', 'std', 'skew', lambda x: x.kurt(), 'count']
        ).rename(columns={'<lambda_0>': 'kurtosis'})

        # 计算每个窗口的时间范围
        time_ranges = [f"{iv.left.strftime('%Y-%m-%d')} ~ {iv.right.strftime('%Y-%m-%d')}"
                       for iv in grouped.index.categories]

        # 存储结果
        window_stats[n] = {
            'stats': grouped,
            'time_ranges': time_ranges
        }

    return {
        'descriptive_stats': desc_stats,
        'outlier_analysis': outlier_analysis,
        'window_stats': window_stats
    }


class FactorEvaluation:
    """
    因子评估类，用于对因子进行全面的统计分析和评估
    """

    def __init__(self, df=None, time_periods=None, future_return_periods=10, factor_col='signals', return_col='future_return',
                 factor_func=None, factor_name=None, *factor_args, **factor_kwargs):
        """
        初始化因子评估器

        参数:
        df: pandas.DataFrame, 可选，包含价格数据的DataFrame，必须包含'close'列
        time_periods: list, 可选，时间周期列表，如['1h', '15m']，会自动读取多个币种数据
        future_return_periods: int, 计算未来收益的周期数，默认为10
        factor_col: str, 因子列名，默认为'signals'
        return_col: str, 收益率列名，默认为'future_return'
        factor_func: callable, 可选，因子计算函数
        factor_name: str, 可选，因子名称，如果提供factor_func则必须提供
        *factor_args, **factor_kwargs: 传递给因子函数的额外参数

        注意: df 和 time_periods 必须提供其中一个
        """
        # 验证输入参数
        if df is None and time_periods is None:
            raise ValueError("必须提供 df 或 time_periods 其中一个参数")
        if df is not None and time_periods is not None:
            raise ValueError("df 和 time_periods 不能同时提供，请选择其中一个")

        self.future_return_periods = future_return_periods
        self.factor_col = factor_col
        self.return_col = return_col
        self.results = {}
        self.multi_symbol_results = {}  # 存储多币种结果
        self.time_periods = time_periods

        # 存储因子函数信息
        self.factor_function_info = {
            'function': None,
            'function_name': None,
            'source_code': None,
            'parameters': {},
            'args': [],
            'kwargs': {},
            'docstring': None,
            'module': None
        }

        if df is not None:
            # 单一数据源模式
            self.df = df.copy()
            self.mode = 'single'
            # 计算未来收益率
            self._calculate_future_return()
        else:
            # 多币种模式
            self.mode = 'multi'
            self.df = None
            self._load_multi_symbol_data()

        # 如果提供了因子函数，直接计算因子
        if factor_func is not None:
            if factor_name is None:
                raise ValueError("提供factor_func时必须同时提供factor_name")
            self.set_factor(factor_func, factor_name, *
                            factor_args, **factor_kwargs)

    def _calculate_future_return(self):
        """计算未来收益率"""
        if 'close' not in self.df.columns:
            raise ValueError("DataFrame必须包含'close'列")

        self.df[self.return_col] = (
            self.df['close'].shift(-self.future_return_periods) /
            self.df['close'] - 1
        )

    def _load_multi_symbol_data(self):
        """
        加载多币种数据
        """
        if not self.time_periods:
            raise ValueError("time_periods 不能为空")

        # 定义要加载的币种
        symbols = ['ETHUSDT', 'BTCUSDT', 'XRPUSDT', 'SOLUSDT']

        # 定义时间范围
        time_ranges = {
            'ETHUSDT': '2020_2025',
            'BTCUSDT': '2020_2025',
            'XRPUSDT': '2020_2025',
            'SOLUSDT': '2020_2025'
        }

        self.symbol_data = {}

        try:
            # 初始化数据服务
            ds = DataService()

            for period in self.time_periods:
                self.symbol_data[period] = {}

                for symbol in symbols:
                    try:
                        # 构造表名
                        table_name = f"{symbol}_{period}_{time_ranges[symbol]}"

                        # 加载数据
                        df = ds[table_name]['2021-10-01':]

                        if df is not None and len(df) > 0:
                            # 计算未来收益率
                            df[self.return_col] = (
                                df['close'].shift(-self.future_return_periods) /
                                df['close'] - 1
                            )

                            self.symbol_data[period][symbol] = df
                            print(f"✅ 加载数据: {table_name}, 长度: {len(df)}")
                        else:
                            print(f"⚠️  数据为空: {table_name}")

                    except Exception as e:
                        print(f"❌ 加载数据失败: {symbol}_{period}, 错误: {str(e)}")

        except Exception as e:
            raise ValueError(f"初始化数据服务失败: {str(e)}")

        # 验证是否有可用数据
        total_datasets = sum(len(period_data)
                             for period_data in self.symbol_data.values())
        if total_datasets == 0:
            raise ValueError("没有成功加载任何数据，请检查数据源")

    def _extract_function_info(self, func, func_name, *args, **kwargs):
        """
        提取函数的详细信息用于JSON存储，包括递归获取所有依赖函数的源代码

        参数:
        func: callable, 函数对象
        func_name: str, 函数名称
        *args, **kwargs: 传递给函数的参数

        返回:
        dict: 包含函数信息的字典
        """
        function_info = {
            'function_name': func_name,
            'source_code': None,
            'complete_source_code': None,  # 包含所有依赖函数的完整源代码
            'dependency_functions': {},    # 依赖函数的源代码
            'global_variables': {},        # 函数使用的全局变量值
            'closure_variables': {},       # 闭包变量值
            'parameters': {},
            'args': list(args),
            'kwargs': dict(kwargs),
            'docstring': None,
            'module': None,
            'signature': None
        }

        try:
            # 获取函数源代码，并确保格式正确
            source_code = inspect.getsource(func)
            # 移除多余的缩进，保持函数定义的原始格式
            import textwrap
            function_info['source_code'] = textwrap.dedent(source_code).strip()

            # 获取完整的源代码（包括依赖函数）
            complete_source, dependencies = self._get_complete_function_source(
                func)
            function_info['complete_source_code'] = complete_source
            function_info['dependency_functions'] = dependencies

        except (OSError, TypeError):
            function_info['source_code'] = f"无法获取源代码 (可能是内置函数或lambda表达式): {func}"
            function_info['complete_source_code'] = function_info['source_code']

        # 获取函数使用的变量值（独立于源代码获取）
        try:
            global_vars, closure_vars = self._extract_function_variables(func)
            function_info['global_variables'] = global_vars
            function_info['closure_variables'] = closure_vars
        except Exception as e:
            function_info['global_variables'] = {'_extraction_error': str(e)}
            function_info['closure_variables'] = {}

        try:
            # 获取函数签名
            sig = inspect.signature(func)
            function_info['signature'] = str(sig)

            # 获取参数信息
            for param_name, param in sig.parameters.items():
                param_info = {
                    'name': param_name,
                    'kind': str(param.kind),
                    'default': str(param.default) if param.default != inspect.Parameter.empty else None,
                    'annotation': str(param.annotation) if param.annotation != inspect.Parameter.empty else None
                }
                function_info['parameters'][param_name] = param_info
        except (ValueError, TypeError):
            function_info['signature'] = "无法获取函数签名"

        try:
            # 获取文档字符串
            function_info['docstring'] = inspect.getdoc(func)
        except:
            function_info['docstring'] = None

        try:
            # 获取模块信息
            function_info['module'] = func.__module__
        except:
            function_info['module'] = None

        return function_info

    def _get_complete_function_source(self, func):
        """
        递归获取函数及其所有依赖函数的完整源代码

        参数:
        func: callable, 主函数对象

        返回:
        tuple: (完整源代码字符串, 依赖函数字典)
        """
        import ast
        import textwrap

        # 存储已处理的函数，避免循环依赖
        processed_functions = set()
        dependency_functions = {}

        def extract_function_calls(source_code):
            """从源代码中提取函数调用"""
            try:
                tree = ast.parse(source_code)
                function_calls = set()

                class FunctionCallVisitor(ast.NodeVisitor):
                    def visit_Call(self, node):
                        if isinstance(node.func, ast.Name):
                            function_calls.add(node.func.id)
                        elif isinstance(node.func, ast.Attribute):
                            # 处理方法调用，如 df.method()
                            if isinstance(node.func.value, ast.Name):
                                # 这里我们主要关注独立函数，不处理方法调用
                                pass
                        self.generic_visit(node)

                visitor = FunctionCallVisitor()
                visitor.visit(tree)
                return function_calls
            except:
                return set()

        def get_function_from_globals(func_name, main_func):
            """从函数的全局命名空间中获取函数对象"""
            try:
                # 获取主函数的全局命名空间
                if hasattr(main_func, '__globals__'):
                    globals_dict = main_func.__globals__
                    if func_name in globals_dict:
                        obj = globals_dict[func_name]
                        # 检查是否是用户定义的函数（不是内置函数或模块）
                        if callable(obj) and hasattr(obj, '__code__'):
                            return obj
                return None
            except:
                return None

        def process_function(func_obj, func_name):
            """递归处理函数及其依赖"""
            if func_name in processed_functions:
                return ""

            processed_functions.add(func_name)

            try:
                # 获取函数源代码
                source = inspect.getsource(func_obj)
                source = textwrap.dedent(source).strip()

                # 存储依赖函数信息
                dependency_functions[func_name] = {
                    'source_code': source,
                    'function_name': func_name,
                    'module': getattr(func_obj, '__module__', None)
                }

                # 提取函数调用
                called_functions = extract_function_calls(source)

                # 递归处理依赖函数
                dependency_sources = []
                for called_func_name in called_functions:
                    if called_func_name != func_name:  # 避免自递归
                        called_func_obj = get_function_from_globals(
                            called_func_name, func_obj)
                        if called_func_obj:
                            dep_source = process_function(
                                called_func_obj, called_func_name)
                            if dep_source:
                                dependency_sources.append(dep_source)

                # 组合源代码：依赖函数 + 主函数
                all_sources = dependency_sources + [source]
                return "\n\n".join(all_sources)

            except (OSError, TypeError):
                return f"# 无法获取函数 {func_name} 的源代码"

        try:
            # 获取主函数名称
            main_func_name = getattr(func, '__name__', 'unknown_function')

            # 处理主函数及其依赖
            complete_source = process_function(func, main_func_name)

            return complete_source, dependency_functions

        except Exception as e:
            error_msg = f"# 获取完整源代码时出错: {str(e)}"
            return error_msg, {}

    def _extract_function_variables(self, func):
        """
        提取函数使用的全局变量和闭包变量的值

        参数:
        func: callable, 函数对象

        返回:
        tuple: (全局变量字典, 闭包变量字典)
        """
        global_variables = {}
        closure_variables = {}

        try:
            # 获取函数的全局命名空间
            if hasattr(func, '__globals__'):
                func_globals = func.__globals__

                # 获取函数源代码中使用的变量名
                try:
                    # 获取完整源代码（包括依赖函数）
                    complete_source, dependencies = self._get_complete_function_source(
                        func)

                    import ast

                    # 使用AST分析完整源代码中的变量引用
                    tree = ast.parse(complete_source)

                    class VariableVisitor(ast.NodeVisitor):
                        def __init__(self):
                            self.variables = set()
                            self.function_params = set()

                        def visit_FunctionDef(self, node):
                            # 记录函数参数
                            for arg in node.args.args:
                                self.function_params.add(arg.arg)
                            self.generic_visit(node)

                        def visit_Name(self, node):
                            if isinstance(node.ctx, ast.Load):
                                # 只关注读取操作的变量，排除函数参数和内置名称
                                if (node.id not in self.function_params and
                                    not node.id.startswith('__') and
                                        node.id not in {'abs', 'len', 'str', 'int', 'float', 'bool', 'list', 'dict', 'set', 'tuple'}):
                                    self.variables.add(node.id)
                            self.generic_visit(node)

                    visitor = VariableVisitor()
                    visitor.visit(tree)

                    # 提取这些变量在全局命名空间中的值
                    for var_name in visitor.variables:
                        if var_name in func_globals:
                            var_value = func_globals[var_name]

                            # 过滤掉函数对象、类和模块
                            if (callable(var_value) or
                                isinstance(var_value, type) or
                                    hasattr(var_value, '__module__')):
                                continue

                            # 只保存可序列化的值
                            try:
                                import json
                                # 尝试序列化以检查是否可序列化
                                json.dumps(var_value, default=str)
                                global_variables[var_name] = var_value
                            except (TypeError, ValueError):
                                # 对于不可序列化的对象，保存其类型和字符串表示
                                global_variables[var_name] = {
                                    'type': str(type(var_value).__name__),
                                    'value': str(var_value)[:200],  # 限制长度
                                    'serializable': False
                                }

                except (OSError, SyntaxError):
                    pass

            # 获取闭包变量
            if hasattr(func, '__closure__') and func.__closure__:
                if hasattr(func, '__code__') and func.__code__.co_freevars:
                    for i, var_name in enumerate(func.__code__.co_freevars):
                        if i < len(func.__closure__):
                            cell = func.__closure__[i]
                            try:
                                var_value = cell.cell_contents
                                # 只保存可序列化的值
                                try:
                                    import json
                                    json.dumps(var_value, default=str)
                                    closure_variables[var_name] = var_value
                                except (TypeError, ValueError):
                                    closure_variables[var_name] = {
                                        'type': str(type(var_value).__name__),
                                        'value': str(var_value)[:200],
                                        'serializable': False
                                    }
                            except ValueError:
                                # 空的cell
                                closure_variables[var_name] = None

        except Exception as e:
            # 如果提取变量失败，记录错误但不影响主流程
            global_variables['_extraction_error'] = str(e)
            import traceback
            global_variables['_extraction_traceback'] = traceback.format_exc()

        return global_variables, closure_variables

    def _save_figure_to_pdf(self, fig, filepath):
        """
        将matplotlib图形保存为PDF文件（静默保存）

        参数:
        fig: matplotlib.figure.Figure, 图形对象
        filepath: str, 保存路径

        返回:
        bool: 是否保存成功
        """
        if fig is None:
            return False

        try:
            # 保存图形为PDF
            fig.savefig(filepath, format='pdf', dpi=300, bbox_inches='tight')
            return True
        except Exception:
            return False

    def _figure_to_base64(self, fig):
        """
        将matplotlib图形转换为base64编码的字符串

        参数:
        fig: matplotlib.figure.Figure, 图形对象

        返回:
        str: base64编码的图片字符串
        """
        if fig is None:
            return None

        try:
            # 创建字节流缓冲区
            buffer = io.BytesIO()

            # 保存图形到缓冲区，使用PNG格式
            fig.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
            buffer.seek(0)

            # 读取字节数据并转换为base64
            image_data = buffer.getvalue()
            buffer.close()

            # 编码为base64字符串
            base64_string = base64.b64encode(image_data).decode('utf-8')

            # 返回完整的data URL格式
            return f"data:image/png;base64,{base64_string}"

        except Exception as e:
            print(f"⚠️  图片转换失败: {str(e)}")
            return f"图片转换失败: {str(e)}"

    def set_factor(self, factor_data_or_func, factor_name='signals', *args, **kwargs):
        """
        设置因子数据或因子计算函数

        参数:
        factor_data_or_func: pandas.Series, numpy.array 或 callable, 因子值或因子计算函数
        factor_name: str, 因子列名
        *args, **kwargs: 传递给因子函数的额外参数
        """
        self.factor_col = factor_name

        if callable(factor_data_or_func):
            # 记录函数信息
            self.factor_function_info = self._extract_function_info(
                factor_data_or_func, factor_name, *args, **kwargs)

            if self.mode == 'single':
                # 单一数据源模式
                try:
                    factor_data = factor_data_or_func(self.df, *args, **kwargs)
                    if isinstance(factor_data, (pd.Series, np.ndarray)):
                        self.df[self.factor_col] = factor_data
                    else:
                        raise ValueError("因子函数必须返回pandas.Series或numpy.array类型")
                    # 删除缺失值
                    self.df = self.df.dropna()
                except Exception as e:
                    raise ValueError(f"因子函数执行失败: {str(e)}")

            elif self.mode == 'multi':
                # 多币种模式
                try:
                    for period in self.symbol_data:
                        for symbol in self.symbol_data[period]:
                            df = self.symbol_data[period][symbol]
                            factor_data = factor_data_or_func(
                                df, *args, **kwargs)
                            if isinstance(factor_data, (pd.Series, np.ndarray)):
                                df[self.factor_col] = factor_data
                                # 删除缺失值
                                self.symbol_data[period][symbol] = df.dropna()
                            else:
                                raise ValueError(
                                    "因子函数必须返回pandas.Series或numpy.array类型")
                    print(f"✅ 因子计算完成: {factor_name}")
                except Exception as e:
                    raise ValueError(f"多币种因子函数执行失败: {str(e)}")

        elif isinstance(factor_data_or_func, (pd.Series, np.ndarray)):
            # 如果传入的是数据，直接使用，清空函数信息
            if self.mode == 'multi':
                raise ValueError("多币种模式不支持直接传入因子数据，请使用因子函数")

            self.factor_function_info = {
                'function_name': factor_name,
                'source_code': 'Direct data input (no function)',
                'parameters': {},
                'args': [],
                'kwargs': {},
                'docstring': 'Factor data provided directly as Series/Array',
                'module': None,
                'signature': None
            }
            self.df[self.factor_col] = factor_data_or_func
            # 删除缺失值
            self.df = self.df.dropna()
        else:
            raise ValueError(
                "factor_data_or_func必须是pandas.Series、numpy.array或可调用函数")

    def stationarity_test(self, alpha=0.05):
        """
        平稳性检验 (ADF检验)

        参数:
        alpha: float, 显著性水平，默认0.05

        返回:
        dict: 包含检验结果的字典
        """
        if self.factor_col not in self.df.columns:
            raise ValueError(f"因子列'{self.factor_col}'不存在")

        adf_result = adfuller(self.df[self.factor_col], autolag='AIC')
        p_value = adf_result[1]

        result = {
            'adf_statistic': adf_result[0],
            'p_value': p_value,
            'critical_values': adf_result[4],
            'is_stationary': p_value < alpha,
            'alpha': alpha
        }

        self.results['stationarity_test'] = result
        return result

    def correlation_analysis(self):
        """
        相关性分析 (Pearson和Spearman相关系数)

        返回:
        dict: 包含IC和Rank_IC的字典
        """
        if self.factor_col not in self.df.columns or self.return_col not in self.df.columns:
            raise ValueError(f"缺少必要的列: {self.factor_col} 或 {self.return_col}")

        # Pearson相关系数 (IC)
        ic = self.df[self.factor_col].corr(
            self.df[self.return_col], method='pearson')

        # Spearman相关系数 (Rank_IC)
        rank_ic = self.df[self.factor_col].corr(
            self.df[self.return_col], method='spearman')

        result = {
            'IC': ic,
            'Rank_IC': rank_ic
        }

        self.results['correlation_analysis'] = result
        return result

    def information_ratio(self, n_groups=10):
        """
        信息比率计算

        参数:
        n_groups: int, 分组数量，默认10

        返回:
        dict: 包含IR值和分组相关系数的字典
        """
        if self.factor_col not in self.df.columns or self.return_col not in self.df.columns:
            raise ValueError(f"缺少必要的列: {self.factor_col} 或 {self.return_col}")

        # 分组
        group_labels = pd.qcut(
            self.df[self.factor_col], n_groups, labels=False, duplicates='drop')
        grouped = self.df.groupby(group_labels)[
            [self.factor_col, self.return_col]]

        # 计算每组的相关系数
        pearson_corrs = []
        for _, group in grouped:
            if len(group) > 1:  # 确保组内有足够的数据计算相关系数
                corr_matrix = group.corr()
                pearson_corrs.append(corr_matrix.iloc[0, 1])

        # 计算IR值
        if len(pearson_corrs) > 1:
            ir_value = np.mean(pearson_corrs) / np.std(pearson_corrs)
        else:
            ir_value = np.nan

        result = {
            'IR': ir_value,
            'group_correlations': pearson_corrs,
            'n_groups': len(pearson_corrs)
        }

        self.results['information_ratio'] = result
        return result

    def group_analysis(self, n_groups=20, show_plot=True):
        """
        分组收益分析

        参数:
        n_groups: int, 分组数量，默认20
        show_plot: bool, 是否显示图表，默认True

        返回:
        tuple: (分组统计结果, 图表对象)
        """
        if self.factor_col not in self.df.columns or self.return_col not in self.df.columns:
            raise ValueError(f"缺少必要的列: {self.factor_col} 或 {self.return_col}")

        analyzer = GroupStats(
            self.df, value_col=self.factor_col, return_col=self.return_col)
        stats, fig = analyzer.analyze_groups(
            n_groups=n_groups, show_plot=show_plot)

        result = {
            'group_stats': stats,
            'figure': fig
        }

        self.results['group_analysis'] = result
        return stats, fig

    def factor_distribution_analysis(self, time_windows=10, show_hist=True, bins=100):
        """
        因子分布分析（数据分割版本）

        参数:
        time_windows: int, 时间窗口数量，默认10
        show_hist: bool, 是否显示直方图，默认True
        bins: int, 直方图的箱数，默认100

        返回:
        dict: 包含分布分析结果的字典
        """
        if self.factor_col not in self.df.columns:
            raise ValueError(f"因子列'{self.factor_col}'不存在")

        # 使用factor_dist模块进行分析
        result = analyze_factor_distribution(
            self.df, factor_col=self.factor_col, time_windows=time_windows)

        # 分割数据：最近10000个点和除最近10000个点
        recent_data, older_data = self._split_data_by_count()

        # 创建分割的直方图
        hist_fig = self._create_split_distribution_plot(
            recent_data, older_data, bins, show_hist)

        # 保存图形对象到结果中（保持图形对象用于后续保存PDF）
        result['figure'] = hist_fig

        self.results['factor_distribution'] = result
        return result

    def _create_split_distribution_plot(self, recent_data, older_data, bins, show_hist):
        """创建分割的因子分布图（在同一张图中）"""
        # 创建单个图
        fig, ax = plt.subplots(figsize=(12, 8))

        # 计算实际数据量用于标签
        recent_count = len(recent_data) if len(recent_data) > 0 else 0
        older_count = len(older_data) if len(older_data) > 0 else 0

        # 格式化数据量显示
        def format_count(count):
            if count >= 1000000:
                return f"{count/1000000:.1f}M"
            elif count >= 1000:
                return f"{count/1000:.1f}K"
            else:
                return str(count)

        # 绘制最近数据的分布
        if len(recent_data) > 0 and self.factor_col in recent_data.columns:
            ax.hist(recent_data[self.factor_col], bins=bins, alpha=0.6,
                    color='lightcoral', edgecolor='darkred', linewidth=1,
                    label=f'Recent ({format_count(recent_count)} points)', density=True)

            # 添加均值线
            mean_recent = recent_data[self.factor_col].mean()
            ax.axvline(mean_recent, color='darkred', linestyle='--', linewidth=2,
                       label=f'Recent Mean: {mean_recent:.4f}')

        # 绘制历史数据的分布
        if len(older_data) > 0 and self.factor_col in older_data.columns:
            ax.hist(older_data[self.factor_col], bins=bins, alpha=0.6,
                    color='skyblue', edgecolor='navy', linewidth=1,
                    label=f'Historical ({format_count(older_count)} points)', density=True)

            # 添加均值线
            mean_older = older_data[self.factor_col].mean()
            ax.axvline(mean_older, color='navy', linestyle='--', linewidth=2,
                       label=f'Historical Mean: {mean_older:.4f}')

        # 设置图表样式
        ax.set_title(f'{self.factor_col} Distribution Comparison - Recent vs Historical',
                     fontsize=14, pad=20)
        ax.set_xlabel(self.factor_col, fontsize=12)
        ax.set_ylabel('Density', fontsize=12)
        ax.grid(axis='y', linestyle='--', alpha=0.3)
        ax.legend()

        plt.tight_layout()

        # 根据参数决定是否显示
        if show_hist:
            plt.show()

        return fig

    def _split_data_by_count(self, recent_count=10000):
        """按数据点数量分割数据：最近N个点和除最近N个点"""
        total_len = len(self.df)

        if total_len <= recent_count:
            # 如果总数据量不足，全部作为最近数据
            return self.df.copy(), pd.DataFrame()

        # 分割数据
        recent_data = self.df.tail(recent_count).copy()
        older_data = self.df.head(total_len - recent_count).copy()

        return recent_data, older_data

    def run_full_evaluation(self, n_groups_ir=10, n_groups_analysis=20, time_windows=10,
                            show_plots=True, print_results=True, run_stationarity_test=False):
        """
        运行完整的因子评估流程

        参数:
        n_groups_ir: int, IR计算的分组数量，默认10
        n_groups_analysis: int, 分组分析的分组数量，默认20
        time_windows: int, 时间窗口数量，默认10
        show_plots: bool, 是否显示图表，默认True
        print_results: bool, 是否打印结果，默认True
        run_stationarity_test: bool, 是否进行平稳性检验，默认True

        返回:
        dict: 包含所有评估结果的字典
        """
        if self.mode == 'single':
            return self._run_single_evaluation(n_groups_ir, n_groups_analysis,
                                               time_windows, show_plots, print_results, run_stationarity_test)
        elif self.mode == 'multi':
            return self._run_multi_evaluation(n_groups_ir, n_groups_analysis,
                                              time_windows, show_plots, print_results, run_stationarity_test)

    def _run_single_evaluation(self, n_groups_ir, n_groups_analysis, time_windows,
                               show_plots, print_results, run_stationarity_test=True):
        """运行单一数据源的评估"""
        if self.factor_col not in self.df.columns:
            raise ValueError(f"请先使用set_factor()方法设置因子数据")

        # 1. 平稳性检验（可选）
        if run_stationarity_test:
            self.stationarity_test()

        # 2. 相关性分析
        self.correlation_analysis()

        # 3. 信息比率计算
        self.information_ratio(n_groups=n_groups_ir)

        # 4. 分组收益分析（先不显示图片）
        self.group_analysis(n_groups=n_groups_analysis, show_plot=False)

        # 5. 因子分布分析（先不显示图片）
        self.factor_distribution_analysis(
            time_windows=time_windows, show_hist=False)

        # 6. 先显示评估结果
        if print_results:
            self._print_single_symbol_results("单币种", "single", self.results)

        # 7. 然后显示图片
        if show_plots:
            self._show_symbol_plots("单币种", "single", self.results)

        # 8. 自动保存结果到JSON文件
        self._save_results_to_json()

        return self.results

    def _run_single_evaluation_no_save(self, n_groups_ir, n_groups_analysis, time_windows,
                                       show_plots, print_results=True, run_stationarity_test=True):
        """运行单一数据源的评估（不保存JSON文件）"""
        if self.factor_col not in self.df.columns:
            raise ValueError(f"请先使用set_factor()方法设置因子数据")

        # 1. 平稳性检验（可选）
        if run_stationarity_test:
            self.stationarity_test()

        # 2. 相关性分析
        self.correlation_analysis()

        # 3. 信息比率计算
        self.information_ratio(n_groups=n_groups_ir)

        # 4. 分组收益分析（先不显示图片）
        self.group_analysis(n_groups=n_groups_analysis, show_plot=False)

        # 5. 因子分布分析（先不显示图片）
        self.factor_distribution_analysis(
            time_windows=time_windows, show_hist=False)

        # 6. 先显示评估结果
        if print_results:
            self._print_single_symbol_results("单币种", "single", self.results)

        # 7. 在显示图片前先保存PDF（避免图形对象被覆盖）
        self._save_current_symbol_pdfs()

        # 8. 然后显示图片
        if show_plots:
            self._show_symbol_plots("单币种", "single", self.results)

        # 注意：这里不保存JSON文件，只返回结果
        return self.results

    def _run_multi_evaluation(self, n_groups_ir, n_groups_analysis, time_windows,
                              show_plots, print_results, run_stationarity_test=True):
        """运行多币种数据的评估"""
        print(f"\n开始多币种因子评估...")
        print(f"时间周期: {self.time_periods}")
        print(f"因子名称: {self.factor_col}")

        # 预先创建保存目录
        figures_dir = self._prepare_multi_results_dirs()

        all_results = {}

        for period in self.symbol_data:
            all_results[period] = {}

            for symbol in self.symbol_data[period]:
                print(f"\n--- 评估 {symbol}_{period} ---")

                # 临时设置当前数据
                original_df = self.df
                original_mode = self.mode

                self.df = self.symbol_data[period][symbol]
                self.mode = 'single'

                # 设置当前币种信息，用于PDF保存
                self._current_symbol_info = {
                    'symbol': symbol,
                    'period': period,
                    'figures_dir': figures_dir
                }

                try:
                    # 运行评估（不显示结果，只计算）
                    symbol_results = self._run_single_evaluation_no_save(
                        n_groups_ir, n_groups_analysis, time_windows,
                        False, False, run_stationarity_test  # 传递平稳性检验参数
                    )

                    all_results[period][symbol] = symbol_results

                    # 立即保存该币种的JSON文件（在显示结果之前）
                    self._save_single_symbol_json(
                        period, symbol, symbol_results)

                    # 然后显示结果和图片
                    if print_results:
                        self._print_single_symbol_results(
                            symbol, period, symbol_results)
                    if show_plots:
                        self._show_symbol_plots(symbol, period, symbol_results)

                except Exception as e:
                    print(f"  ❌ 评估失败: {str(e)}")
                    all_results[period][symbol] = {'error': str(e)}

                finally:
                    # 恢复原始设置
                    self.df = original_df
                    self.mode = original_mode
                    # 清除当前币种信息
                    if hasattr(self, '_current_symbol_info'):
                        delattr(self, '_current_symbol_info')

        # 保存多币种结果
        self.multi_symbol_results = all_results

        # JSON文件已经在评估过程中单独保存了，这里只需要打印完成信息
        # print(f"✅ 多币种评估完成！已为每个币种单独保存JSON文件到: {self._jsons_dir}")

        return all_results

    def _prepare_multi_results_dirs(self):
        """
        预先创建多币种评估的结果目录

        返回:
        str: figures目录路径
        """
        # 创建results主文件夹（如果不存在）
        results_dir = os.path.join(os.path.dirname(__file__), 'results')
        os.makedirs(results_dir, exist_ok=True)

        # 创建figures和jsons主文件夹
        figures_main_dir = os.path.join(results_dir, 'figures')
        jsons_main_dir = os.path.join(results_dir, 'jsons')
        os.makedirs(figures_main_dir, exist_ok=True)
        os.makedirs(jsons_main_dir, exist_ok=True)

        # 生成文件夹名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        periods_str = '_'.join(self.time_periods)
        folder_name = f"factor_evaluation_multi_{self.factor_col}_{periods_str}_{timestamp}"

        # 在figures和jsons主文件夹下创建评估结果子文件夹
        figures_dir = os.path.join(figures_main_dir, folder_name)
        jsons_dir = os.path.join(jsons_main_dir, folder_name)
        os.makedirs(figures_dir, exist_ok=True)
        os.makedirs(jsons_dir, exist_ok=True)

        # 保存jsons_dir供后续使用
        self._jsons_dir = jsons_dir

        return figures_dir

    def _save_single_symbol_json(self, period, symbol, symbol_results):
        """
        保存单个币种的JSON文件（在显示结果之前立即保存）

        参数:
        period: 时间周期
        symbol: 币种名称
        symbol_results: 该币种的评估结果
        """
        if not hasattr(self, '_jsons_dir'):
            print("❌ JSON目录未初始化")
            return

        jsons_dir = self._jsons_dir

        if 'error' in symbol_results:
            # 如果有错误，保存错误信息
            error_data = {
                'metadata': {
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'factor_name': self.factor_col,
                    'return_column': self.return_col,
                    'future_return_periods': self.future_return_periods,
                    'period': period,
                    'symbol': symbol,
                    'evaluation_mode': 'multi_symbol',
                    'status': 'error'
                },
                'factor_function': self.factor_function_info.copy() if self.factor_function_info else {},
                'evaluation_results': symbol_results
            }

            # 保存错误文件
            json_filename = f"{self.factor_col}_{period}_{symbol}_error.json"
            json_filepath = os.path.join(jsons_dir, json_filename)

            try:
                with open(json_filepath, 'w', encoding='utf-8') as f:
                    json.dump(error_data, f, ensure_ascii=False,
                              indent=2, default=str)
            except Exception as e:
                print(f"❌ 保存错误JSON文件时出错 ({symbol}_{period}): {str(e)}")

        else:
            # 正常结果，使用现有的序列化方法
            original_results = self.results
            self.results = symbol_results
            serialized_result = self._prepare_results_for_json()
            self.results = original_results

            # 为每个币种创建独立的元数据
            symbol_metadata = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'factor_name': self.factor_col,
                'return_column': self.return_col,
                'future_return_periods': self.future_return_periods,
                'period': period,
                'symbol': symbol,
                'evaluation_mode': 'multi_symbol',
                'status': 'success'
            }

            # 添加数据统计信息（如果可用）
            if hasattr(self, 'symbol_data') and period in self.symbol_data and symbol in self.symbol_data[period]:
                symbol_df = self.symbol_data[period][symbol]
                symbol_metadata.update({
                    'data_length': len(symbol_df),
                    'data_start_time': str(symbol_df.index.min()) if hasattr(symbol_df.index, 'min') else 'N/A',
                    'data_end_time': str(symbol_df.index.max()) if hasattr(symbol_df.index, 'max') else 'N/A'
                })

            # 合并该币种的完整结果
            symbol_final_results = {
                'metadata': symbol_metadata,
                'factor_function': self.factor_function_info.copy() if self.factor_function_info else {},
                'evaluation_results': serialized_result
            }

            # 保存该币种的JSON文件
            json_filename = f"{self.factor_col}_{period}_{symbol}_results.json"
            json_filepath = os.path.join(jsons_dir, json_filename)

            try:
                with open(json_filepath, 'w', encoding='utf-8') as f:
                    json.dump(symbol_final_results, f,
                              ensure_ascii=False, indent=2, default=str)
                # print(f"✅ 已保存 {symbol}_{period} 的JSON文件")
            except Exception as e:
                # print(f"❌ 保存JSON文件时出错 ({symbol}_{period}): {str(e)}")
                pass

    def _save_multi_results_to_json_only(self):
        """
        多币种评估结果保存方法（已废弃，JSON文件在评估过程中实时保存）
        """
        # 这个方法已经不需要了，因为JSON文件在评估过程中已经单独保存了
        pass

    def _print_single_symbol_results(self, symbol, period, symbol_results):
        """
        打印单个币种的详细评估结果

        参数:
        symbol: 币种名称
        period: 时间周期
        symbol_results: 该币种的评估结果
        """
        print(f"\n📊 {symbol} ({period}) 详细评估结果:")
        print("-" * 50)

        # 平稳性检验结果
        if 'stationarity_test' in symbol_results:
            stationarity = symbol_results['stationarity_test']
            print(f"📈 平稳性检验 (ADF):")
            print(f"   p_value: {stationarity['p_value']:.6f}")
            print(f"   是否平稳: {'是' if stationarity['is_stationary'] else '否'}")

        # 相关性分析结果
        if 'correlation_analysis' in symbol_results:
            correlation = symbol_results['correlation_analysis']
            print(f"🔗 相关性分析:")
            print(f"   IC (Pearson): {correlation['IC']:.6f}")
            print(f"   Rank_IC (Spearman): {correlation['Rank_IC']:.6f}")

        # 信息比率结果
        if 'information_ratio' in symbol_results:
            ir = symbol_results['information_ratio']
            print(f"📊 信息比率:")
            print(f"   IR: {ir['IR']:.6f}")
            print(f"   有效分组数: {ir['n_groups']}")

        # # 分组分析结果
        # if 'group_analysis' in symbol_results:
        #     group_analysis = symbol_results['group_analysis']
        #     if 'group_stats' in group_analysis:
        #         group_stats = group_analysis['group_stats']
        #         if 'return_mean' in group_stats:
        #             print(f"📈 分组分析:")
        #             for group, ret in group_stats['return_mean'].items():
        #                 print(f"   分组 {group}: {ret:.6f}")

        # 因子分布统计
        if 'factor_distribution' in symbol_results:
            factor_dist = symbol_results['factor_distribution']
            print(f"📊 因子分布:")
            if 'mean' in factor_dist:
                print(f"   均值: {factor_dist['mean']:.6f}")
            if 'std' in factor_dist:
                print(f"   标准差: {factor_dist['std']:.6f}")
            if 'skewness' in factor_dist:
                print(f"   偏度: {factor_dist['skewness']:.6f}")
            if 'kurtosis' in factor_dist:
                print(f"   峰度: {factor_dist['kurtosis']:.6f}")

        # 数据概况
        data_length = len(self.df) if hasattr(
            self, 'df') and self.df is not None else 'N/A'
        print(f"📋 数据概况:")
        print(f"   数据长度: {data_length}")
        print(f"   因子列: {self.factor_col}")
        print(f"   收益率列: {self.return_col}")
        print(f"   未来收益周期: {self.future_return_periods}")

        print("-" * 50)

    def _show_symbol_plots(self, symbol, period, symbol_results):
        """
        显示单个币种的图片

        参数:
        symbol: 币种名称
        period: 时间周期
        symbol_results: 该币种的评估结果
        """
        import matplotlib.pyplot as plt

        print(f"\n🖼️  {symbol} ({period}) 图片展示:")
        print("-" * 40)
        plots_shown = 0

        # 1. 显示分组收益图
        if 'group_analysis' in symbol_results and 'figure' in symbol_results['group_analysis']:
            figure = symbol_results['group_analysis']['figure']
            if figure is not None:
                print(f"📊 显示分组分析图...")
                # 使用plt.show()显示图形
                plt.show()
                plots_shown += 1

        # 2. 显示因子分布图
        if 'factor_distribution' in symbol_results and 'figure' in symbol_results['factor_distribution']:
            figure = symbol_results['factor_distribution']['figure']
            if figure is not None:
                print(f"📈 显示因子分布图...")
                # 使用plt.show()显示图形
                plt.show()
                plots_shown += 1

        if plots_shown == 0:
            print("⚠️  没有找到可显示的图片")
        else:
            print(f"✅ 已显示 {plots_shown} 个图片")

        print("-" * 40)

    def _save_current_symbol_pdfs(self):
        """
        保存当前币种的PDF图片（在显示前保存，避免图形对象被覆盖）
        """
        if not hasattr(self, '_current_symbol_info'):
            return

        symbol = self._current_symbol_info.get('symbol', 'unknown')
        period = self._current_symbol_info.get('period', 'unknown')
        figures_dir = self._current_symbol_info.get('figures_dir', '')

        if not figures_dir or not os.path.exists(figures_dir):
            return

        # 1. 保存分组分析图片
        if 'group_analysis' in self.results and 'figure' in self.results['group_analysis']:
            figure = self.results['group_analysis']['figure']
            if figure is not None:
                pdf_filename = f"{self.factor_col}_{period}_{symbol}_group_analysis.pdf"
                pdf_filepath = os.path.join(figures_dir, pdf_filename)
                self._save_figure_to_pdf(figure, pdf_filepath)

        # 2. 保存因子分布图片
        if 'factor_distribution' in self.results and 'figure' in self.results['factor_distribution']:
            figure = self.results['factor_distribution']['figure']
            if figure is not None:
                pdf_filename = f"{self.factor_col}_{period}_{symbol}_distribution.pdf"
                pdf_filepath = os.path.join(figures_dir, pdf_filename)
                self._save_figure_to_pdf(figure, pdf_filepath)

    def _redraw_group_analysis_plot(self, plot_data):
        """重新绘制分组分析图"""
        import matplotlib.pyplot as plt

        group_returns = plot_data['group_returns']
        n_groups = plot_data['n_groups']
        return_col = plot_data['return_col']

        # 创建新的图形
        plt.figure(figsize=(12, 6))

        # 绘制柱状图
        bars = plt.bar(
            x=group_returns.index,
            height=group_returns.values,
            width=0.6,
            color='skyblue',
            edgecolor='navy',
            linewidth=1
        )

        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height,
                     f'{height:.4f}',
                     ha='center', va='bottom',
                     fontsize=9)

        # 设置图表样式
        plt.title(f'Mean {return_col} by Group ({n_groups} groups)')
        plt.xlabel('Group Number')
        plt.ylabel(f'Mean {return_col}')
        plt.xticks(range(n_groups))
        plt.grid(axis='y', linestyle='--', alpha=0.5)

        # 显示图形
        plt.show()

    def _redraw_factor_distribution_plot(self, plot_data):
        """重新绘制因子分布图"""
        import matplotlib.pyplot as plt

        factor_data = plot_data['factor_data']
        factor_col = plot_data['factor_col']
        bins = plot_data['bins']

        # 创建新的图形
        plt.figure(figsize=(10, 6))
        factor_data.hist(bins=bins)
        plt.title(f'{factor_col} Distribution')
        plt.xlabel(factor_col)
        plt.ylabel('Frequency')
        plt.tight_layout()

        # 显示图形
        plt.show()

    def load_and_display_results(self, result_path: str = None,
                                 show_summary: bool = True,
                                 show_function: bool = False,
                                 show_detailed: bool = True,
                                 show_table: bool = False,
                                 show_images: bool = False,
                                 export_results: bool = False) -> 'FactorResultLoader':
        """
        便捷方法：加载并显示结果

        参数:
        result_path: 结果文件夹路径或JSON文件路径，如果为None则使用最新生成的文件夹
        show_summary: 是否显示摘要
        show_function: 是否显示函数信息
        show_detailed: 是否显示详细结果
        show_table: 是否显示性能表格
        show_images: 是否显示图片（PDF文件信息）
        export_results: 是否导出结果

        返回:
        FactorResultLoader: 结果加载器实例
        """
        # 如果没有指定路径，尝试找到最新的结果文件夹
        if result_path is None:
            result_path = self._find_latest_json_file()

        if not result_path:
            print("❌ 没有找到结果文件夹")
            return None

        try:
            # 创建结果加载器
            loader = FactorResultLoader(result_path)

            # 显示摘要
            if show_summary:
                loader.print_summary()

            # 显示函数信息
            if show_function:
                loader.print_factor_function()

            # 显示详细结果
            if show_detailed:
                loader.print_detailed_results()

            # 显示性能表格
            if show_table:
                loader.print_performance_table()

                # 多币种模式不显示额外的对比信息

            # 显示图片
            if show_images:
                loader.show_all_images()

            # 导出结果
            if export_results:
                loader.export_results()

            return loader

        except Exception as e:
            print(f"❌ 加载结果失败: {str(e)}")
            return None

    def _find_latest_json_file(self) -> Optional[str]:
        """查找最新的结果文件夹"""
        results_dir = os.path.join(os.path.dirname(__file__), 'results')

        if not os.path.exists(results_dir):
            return None

        result_folders = [f for f in os.listdir(results_dir)
                          if os.path.isdir(os.path.join(results_dir, f)) and f.startswith('factor_evaluation')]

        if not result_folders:
            return None

        # 按修改时间排序，获取最新的文件夹
        latest_folder = max(result_folders,
                            key=lambda x: os.path.getmtime(os.path.join(results_dir, x)))

        return os.path.join(results_dir, latest_folder)

    def create_result_loader(self, result_path: str) -> 'FactorResultLoader':
        """
        创建结果加载器实例

        参数:
        result_path: 结果文件夹路径或JSON文件路径

        返回:
        FactorResultLoader: 结果加载器实例
        """
        return FactorResultLoader(result_path)

    def _save_results_to_json(self):
        """
        将评估结果保存到统一的文件夹结构（results/figures/和results/jsons/下按评估结果创建子文件夹）
        """
        # 创建results主文件夹（如果不存在）
        results_dir = os.path.join(os.path.dirname(__file__), 'results')
        os.makedirs(results_dir, exist_ok=True)

        # 创建figures和jsons主文件夹
        figures_main_dir = os.path.join(results_dir, 'figures')
        jsons_main_dir = os.path.join(results_dir, 'jsons')
        os.makedirs(figures_main_dir, exist_ok=True)
        os.makedirs(jsons_main_dir, exist_ok=True)

        # 生成文件夹名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        folder_name = f"factor_evaluation_{self.factor_col}_{timestamp}"

        # 在figures和jsons主文件夹下创建评估结果子文件夹
        figures_dir = os.path.join(figures_main_dir, folder_name)
        jsons_dir = os.path.join(jsons_main_dir, folder_name)
        os.makedirs(figures_dir, exist_ok=True)
        os.makedirs(jsons_dir, exist_ok=True)

        # 准备可序列化的结果数据
        json_results = self._prepare_results_for_json()

        # 添加元数据
        metadata = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'factor_name': self.factor_col,
            'return_column': self.return_col,
            'future_return_periods': self.future_return_periods,
            'data_length': len(self.df),
            'data_start_time': str(self.df.index.min()) if hasattr(self.df.index, 'min') else 'N/A',
            'data_end_time': str(self.df.index.max()) if hasattr(self.df.index, 'max') else 'N/A'
        }

        # 添加因子函数信息
        factor_function_metadata = self.factor_function_info.copy(
        ) if self.factor_function_info else {}

        # 合并元数据和结果
        final_results = {
            'metadata': metadata,
            'factor_function': factor_function_metadata,
            'evaluation_results': json_results
        }

        # 保存JSON文件到jsons子文件夹
        json_filename = f"{self.factor_col}_results.json"
        json_filepath = os.path.join(jsons_dir, json_filename)

        try:
            with open(json_filepath, 'w', encoding='utf-8') as f:
                json.dump(final_results, f, ensure_ascii=False,
                          indent=2, default=str)
        except Exception as e:
            print(f"❌ 保存JSON文件时出错: {str(e)}")
            return

        # 保存所有PDF图片到figures子文件夹（静默保存）
        # 1. 保存分组分析图片
        if 'group_analysis' in self.results and 'figure' in self.results['group_analysis']:
            figure = self.results['group_analysis']['figure']
            if figure is not None:
                pdf_filename = f"{self.factor_col}_group_analysis.pdf"
                pdf_filepath = os.path.join(figures_dir, pdf_filename)
                self._save_figure_to_pdf(figure, pdf_filepath)

        # 2. 保存因子分布图片
        if 'factor_distribution' in self.results and 'figure' in self.results['factor_distribution']:
            figure = self.results['factor_distribution']['figure']
            if figure is not None:
                pdf_filename = f"{self.factor_col}_distribution.pdf"
                pdf_filepath = os.path.join(figures_dir, pdf_filename)
                self._save_figure_to_pdf(figure, pdf_filepath)

    def _print_results(self):
        """打印评估结果"""
        print("=" * 60)
        print("因子评估结果")
        print("=" * 60)

        # 平稳性检验结果
        if 'stationarity_test' in self.results:
            stationarity = self.results['stationarity_test']
            print(f"\n【平稳性检验 (ADF检验)】")
            print(f"p_value: {stationarity['p_value']:.6f}")
            print(f"是否平稳: {'是' if stationarity['is_stationary'] else '否'}")
            print("=" * 40)

        # 相关性分析结果
        if 'correlation_analysis' in self.results:
            correlation = self.results['correlation_analysis']
            print(f"\n【相关性分析】")
            print(f"IC (Pearson): {correlation['IC']:.6f}")
            print(f"Rank_IC (Spearman): {correlation['Rank_IC']:.6f}")
            print("=" * 40)

        # 信息比率结果
        if 'information_ratio' in self.results:
            ir = self.results['information_ratio']
            print(f"\n【信息比率】")
            print(f"IR: {ir['IR']:.6f}")
            print(f"有效分组数: {ir['n_groups']}")
            print("=" * 40)

        print(f"\n【数据概况】")
        print(f"数据总量: {len(self.df)}")
        print(f"因子列: {self.factor_col}")
        print(f"收益率列: {self.return_col}")
        print(f"未来收益周期: {self.future_return_periods}")
        print("=" * 60)

    def _convert_to_serializable(self, obj):
        """
        递归地将对象转换为JSON可序列化的格式
        """
        if isinstance(obj, dict):
            result = {}
            for k, v in obj.items():
                # 处理键
                if hasattr(k, 'left') and hasattr(k, 'right'):  # pandas Interval对象
                    key_str = f"[{k.left}, {k.right})"
                else:
                    key_str = str(k)
                # 处理值
                result[key_str] = self._convert_to_serializable(v)
            return result
        elif isinstance(obj, (list, tuple)):
            return [self._convert_to_serializable(item) for item in obj]
        elif hasattr(obj, 'to_dict'):  # pandas DataFrame/Series
            return self._convert_to_serializable(obj.to_dict())
        elif hasattr(obj, 'tolist'):  # numpy array
            return obj.tolist()
        elif hasattr(obj, 'item'):  # numpy scalar
            return obj.item()
        elif pd.isna(obj):  # pandas NaN
            return None
        elif isinstance(obj, (np.integer, np.floating)):
            return float(obj)
        elif isinstance(obj, (pd.Timestamp, pd.Timedelta)):
            return str(obj)
        else:
            return obj

    def _prepare_results_for_json(self):
        """
        准备结果数据用于JSON序列化
        将不可序列化的对象转换为可序列化的格式，不包含图片数据
        """
        json_results = {}

        for key, value in self.results.items():
            if key == 'group_analysis':
                # 处理分组分析结果，不保存图片数据
                group_result = {
                    'group_stats': self._convert_to_serializable(value['group_stats'])
                }

                # 只记录图片信息，不保存图片数据
                if 'figure' in value and value['figure'] is not None:
                    group_result['figure_info'] = 'Figure saved as separate PDF file'
                else:
                    group_result['figure_info'] = 'No figure available'

                json_results[key] = group_result

            elif key == 'factor_distribution':
                # 处理因子分布分析结果，不保存图片数据
                dist_result = self._convert_to_serializable(value)

                # 移除图片对象，只保留图片信息
                if isinstance(dist_result, dict):
                    if 'figure' in dist_result:
                        # 检查是否有图片
                        if value.get('figure') is not None:
                            dist_result['figure_info'] = 'Figure saved as separate PDF file'
                        else:
                            dist_result['figure_info'] = 'No figure available'
                        # 移除图片对象
                        dist_result.pop('figure', None)

                json_results[key] = dist_result
            else:
                # 处理其他结果（平稳性检验、相关性分析、信息比率）
                json_results[key] = self._convert_to_serializable(value)

        return json_results


class FactorResultLoader:
    """
    因子评估结果加载器

    功能:
    1. 加载单币种和多币种评估结果
    2. 统一的结果展示格式
    3. 图片解码和显示
    4. 结果统计和对比
    """

    def __init__(self, result_path: str):
        """
        初始化结果加载器

        参数:
        result_path: 结果文件夹路径或JSON文件路径
        """
        self.result_path = result_path
        self.json_file_path = None
        self.result_folder = None
        self.data = None
        self.metadata = None
        self.factor_function = None
        self.evaluation_results = None
        self.evaluation_mode = None

        # 确定路径类型并加载数据
        self._determine_path_type()
        self._load_data()
        self._parse_data()

    def _determine_path_type(self):
        """确定路径类型（文件夹还是JSON文件）"""
        if os.path.isdir(self.result_path):
            # 是文件夹，查找JSON文件
            self.result_folder = self.result_path

            # 检查是否是新的文件夹结构（results/jsons/评估结果文件夹/）
            parent_dir = os.path.dirname(self.result_path)
            if os.path.basename(parent_dir) == 'jsons':
                # 这是jsons下的评估结果文件夹，直接在此文件夹中查找JSON文件
                json_files = [f for f in os.listdir(
                    self.result_path) if f.endswith('.json')]
                if json_files:
                    self.json_file_path = os.path.join(
                        self.result_path, json_files[0])
                    # 设置result_folder为对应的figures文件夹
                    results_dir = os.path.dirname(parent_dir)
                    figures_dir = os.path.join(
                        results_dir, 'figures', os.path.basename(self.result_path))
                    if os.path.exists(figures_dir):
                        self.result_folder = figures_dir
                    return
                else:
                    raise ValueError(f"在文件夹 {self.result_path} 中没有找到JSON文件")

            # 首先在jsons子文件夹中查找JSON文件（旧版本兼容）
            jsons_dir = os.path.join(self.result_path, 'jsons')
            if os.path.exists(jsons_dir):
                json_files = [f for f in os.listdir(
                    jsons_dir) if f.endswith('.json')]
                if json_files:
                    self.json_file_path = os.path.join(
                        jsons_dir, json_files[0])
                    return

            # 如果jsons子文件夹不存在或没有JSON文件，则在主文件夹中查找（兼容旧版本）
            json_files = [f for f in os.listdir(
                self.result_path) if f.endswith('.json')]
            if json_files:
                self.json_file_path = os.path.join(
                    self.result_path, json_files[0])
            else:
                raise ValueError(f"在文件夹 {self.result_path} 中没有找到JSON文件")
        elif os.path.isfile(self.result_path) and self.result_path.endswith('.json'):
            # 是JSON文件
            self.json_file_path = self.result_path
            # 检查是否是新的文件夹结构
            parent_dir = os.path.dirname(self.result_path)
            if os.path.basename(os.path.dirname(parent_dir)) == 'jsons':
                # 这是新结构，设置result_folder为对应的figures文件夹
                results_dir = os.path.dirname(os.path.dirname(parent_dir))
                figures_dir = os.path.join(
                    results_dir, 'figures', os.path.basename(parent_dir))
                if os.path.exists(figures_dir):
                    self.result_folder = figures_dir
                else:
                    self.result_folder = parent_dir
            else:
                self.result_folder = parent_dir
        else:
            raise ValueError(f"路径 {self.result_path} 不是有效的文件夹或JSON文件")

    def _load_data(self):
        """加载JSON数据"""
        try:
            with open(self.json_file_path, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"✅ 成功加载结果文件: {os.path.basename(self.json_file_path)}")
        except Exception as e:
            raise ValueError(f"❌ 加载JSON文件失败: {str(e)}")

    def _parse_data(self):
        """解析数据结构"""
        self.metadata = self.data.get('metadata', {})
        self.factor_function = self.data.get('factor_function', {})
        self.evaluation_results = self.data.get('evaluation_results', {})

        # 判断评估模式
        self.evaluation_mode = self.metadata.get('evaluation_mode', 'single')
        if 'time_periods' in self.metadata and isinstance(self.metadata['time_periods'], list):
            self.evaluation_mode = 'multi_symbol'

    def get_summary(self) -> Dict[str, Any]:
        """获取结果摘要信息"""
        summary = {
            'file_name': os.path.basename(self.json_file_path),
            'factor_name': self.metadata.get('factor_name', 'Unknown'),
            'evaluation_mode': self.evaluation_mode,
            'timestamp': self.metadata.get('timestamp', 'Unknown'),
        }

        if self.evaluation_mode == 'multi_symbol':
            summary.update({
                'time_periods': self.metadata.get('time_periods', []),
                'total_symbols': self.metadata.get('total_symbols', 0),
                'symbols_by_period': self.metadata.get('symbols_by_period', {})
            })
        else:
            summary.update({
                'data_length': self.metadata.get('data_length', 0),
                'future_return_periods': self.metadata.get('future_return_periods', 0),
                'data_start_time': self.metadata.get('data_start_time', 'Unknown'),
                'data_end_time': self.metadata.get('data_end_time', 'Unknown')
            })

        return summary

    def print_summary(self):
        """打印结果摘要"""
        summary = self.get_summary()

        print("\n" + "=" * 80)
        print(f"📊 因子评估结果摘要")
        print("=" * 80)
        print(f"文件名称: {summary['file_name']}")
        print(f"因子名称: {summary['factor_name']}")
        print(f"评估模式: {summary['evaluation_mode']}")
        print(f"评估时间: {summary['timestamp']}")

        if self.evaluation_mode == 'multi_symbol':
            print(f"时间周期: {summary['time_periods']}")
            print(f"总币种数: {summary['total_symbols']}")
            print("各周期币种:")
            for period, symbols in summary['symbols_by_period'].items():
                print(f"  {period}: {symbols}")
        else:
            print(f"数据长度: {summary['data_length']}")
            print(f"未来收益期数: {summary['future_return_periods']}")
            print(
                f"数据时间范围: {summary['data_start_time']} ~ {summary['data_end_time']}")

    def print_factor_function(self, show_complete_source=True):
        """
        打印因子函数信息

        参数:
        show_complete_source: bool, 是否显示完整源代码（包括依赖函数），默认True
        """
        print("\n" + "=" * 80)
        print("🔧 因子函数信息")
        print("=" * 80)

        func_name = self.factor_function.get('function_name', 'Unknown')
        signature = self.factor_function.get('signature', 'Unknown')
        docstring = self.factor_function.get('docstring', 'No description')
        kwargs = self.factor_function.get('kwargs', {})

        print(f"函数名称: {func_name}")
        print(f"函数签名: {signature}")
        print(f"函数说明: {docstring}")
        print(f"调用参数: {kwargs}")

        # 显示依赖函数信息
        dependency_functions = self.factor_function.get(
            'dependency_functions', {})
        if dependency_functions:
            print(f"\n依赖函数数量: {len(dependency_functions)}")
            print("依赖函数列表:", ", ".join(dependency_functions.keys()))

        # 显示变量信息
        global_vars = self.factor_function.get('global_variables', {})
        closure_vars = self.factor_function.get('closure_variables', {})

        if global_vars:
            print(f"\n全局变量数量: {len(global_vars)}")
            print("全局变量:")
            for var_name, var_value in global_vars.items():
                if isinstance(var_value, dict) and 'serializable' in var_value:
                    print(
                        f"  {var_name}: {var_value['type']} = {var_value['value']}")
                else:
                    print(
                        f"  {var_name}: {type(var_value).__name__} = {var_value}")

        if closure_vars:
            print(f"\n闭包变量数量: {len(closure_vars)}")
            print("闭包变量:")
            for var_name, var_value in closure_vars.items():
                if isinstance(var_value, dict) and 'serializable' in var_value:
                    print(
                        f"  {var_name}: {var_value['type']} = {var_value['value']}")
                else:
                    print(
                        f"  {var_name}: {type(var_value).__name__} = {var_value}")

        # 显示源代码
        if show_complete_source:
            complete_source = self.factor_function.get(
                'complete_source_code', '')
            if complete_source:
                print("\n完整源代码（包含所有依赖函数）:")
                print("-" * 60)
                print(complete_source)
                print("-" * 60)
            else:
                # 回退到原始源代码
                source_code = self.factor_function.get('source_code', '')
                if source_code:
                    print("\n主函数源代码:")
                    print("-" * 40)
                    print(source_code)
                    print("-" * 40)
        else:
            # 只显示主函数源代码
            source_code = self.factor_function.get('source_code', '')
            if source_code:
                print("\n主函数源代码:")
                print("-" * 40)
                print(source_code)
                print("-" * 40)

            # 单独显示依赖函数源代码
            if dependency_functions:
                print("\n依赖函数源代码:")
                print("-" * 40)
                for dep_name, dep_info in dependency_functions.items():
                    print(f"\n# 函数: {dep_name}")
                    print(dep_info.get('source_code', '无法获取源代码'))
                    print()
                print("-" * 40)

    def _format_single_results(self, results: Dict[str, Any], symbol_name: str = "") -> Dict[str, Any]:
        """格式化单一结果为统一格式"""
        formatted = {
            'symbol': symbol_name or 'Single',
            'stationarity_test': results.get('stationarity_test', {}),
            'correlation_analysis': results.get('correlation_analysis', {}),
            'information_ratio': results.get('information_ratio', {}),
            'group_analysis': results.get('group_analysis', {}),
            'factor_distribution': results.get('factor_distribution', {})
        }
        return formatted

    def get_all_results(self) -> Dict[str, Any]:
        """获取所有结果，统一格式"""
        if self.evaluation_mode == 'multi_symbol':
            # 多币种模式：按时间周期和币种组织
            all_results = {}
            for period, period_results in self.evaluation_results.items():
                all_results[period] = {}
                for symbol, symbol_results in period_results.items():
                    all_results[period][symbol] = self._format_single_results(
                        symbol_results, f"{symbol}_{period}"
                    )
            return all_results
        else:
            # 单币种模式：直接格式化
            return {
                'single': {
                    'data': self._format_single_results(self.evaluation_results, 'Single')
                }
            }

    def print_detailed_results(self, show_distribution: bool = True):
        """打印详细评估结果"""
        print("\n" + "=" * 80)
        print("📈 详细评估结果")
        print("=" * 80)

        all_results = self.get_all_results()

        for period_or_mode, period_data in all_results.items():
            if self.evaluation_mode == 'multi_symbol':
                print(f"\n🕐 时间周期: {period_or_mode}")
                print("-" * 60)

            for symbol, results in period_data.items():
                symbol_name = results['symbol']
                print(f"\n📊 {symbol_name}")
                print("─" * 40)

                # 1. 平稳性检验
                stationarity = results['stationarity_test']
                if stationarity:
                    is_stationary = stationarity.get('is_stationary', False)
                    p_value = stationarity.get('p_value', 0)
                    adf_stat = stationarity.get('adf_statistic', 0)

                    status = "✅ 平稳" if is_stationary else "❌ 非平稳"
                    print(f"平稳性检验: {status}")
                    print(f"  ADF统计量: {adf_stat:.6f}")
                    print(f"  P值: {p_value:.6f}")

                # 2. 相关性分析
                correlation = results['correlation_analysis']
                if correlation:
                    ic = correlation.get('IC', 0)
                    rank_ic = correlation.get('Rank_IC', 0)
                    print(f"相关性分析:")
                    print(f"  IC (Pearson): {ic:.6f}")
                    print(f"  Rank_IC (Spearman): {rank_ic:.6f}")

                # 3. 信息比率
                ir_result = results['information_ratio']
                if ir_result:
                    ir = ir_result.get('IR', 0)
                    n_groups = ir_result.get('n_groups', 0)
                    print(f"信息比率:")
                    print(f"  IR值: {ir:.6f}")
                    print(f"  分组数: {n_groups}")

                # 4. 分组分析摘要
                group_analysis = results['group_analysis']
                if group_analysis and 'group_stats' in group_analysis:
                    group_stats = group_analysis['group_stats']
                    if 'return_mean' in group_stats:
                        returns = list(group_stats['return_mean'].values())
                        if returns:
                            print(f"分组分析:")
                            print(f"  最低组收益: {min(returns):.6f}")
                            print(f"  最高组收益: {max(returns):.6f}")
                            print(f"  收益差: {max(returns) - min(returns):.6f}")

                # 5. 因子分布（可选）
                if show_distribution:
                    factor_dist = results['factor_distribution']
                    if factor_dist and 'descriptive_stats' in factor_dist:
                        desc_stats = factor_dist['descriptive_stats']
                        print(f"因子分布:")
                        print(f"  均值: {desc_stats.get('mean', 0):.6f}")
                        print(f"  标准差: {desc_stats.get('std', 0):.6f}")
                        print(f"  偏度: {desc_stats.get('skew', 0):.6f}")
                        print(f"  峰度: {desc_stats.get('kurtosis', 0):.6f}")

    def extract_and_show_image(self, period: str = None, symbol: str = None,
                               save_path: str = None, show_image: bool = True) -> Optional[str]:
        """
        提取并显示图片

        参数:
        period: 时间周期（多币种模式需要）
        symbol: 币种名称（多币种模式需要）
        save_path: 保存路径（可选）
        show_image: 是否显示图片

        返回:
        str: 保存的图片路径（如果保存了的话）
        """
        # 查找PDF文件
        pdf_path = None

        if self.evaluation_mode == 'multi_symbol':
            if not period or not symbol:
                print("❌ 多币种模式需要指定 period 和 symbol 参数")
                return None

            # 构造PDF文件名
            factor_name = self.metadata.get('factor_name', 'unknown')
            pdf_filename = f"{factor_name}_{period}_{symbol}_group_analysis.pdf"

            # 新结构：PDF文件在result_folder中（已经是figures文件夹）
            pdf_path = os.path.join(self.result_folder, pdf_filename)
        else:
            # 单币种模式
            factor_name = self.metadata.get('factor_name', 'unknown')
            pdf_filename = f"{factor_name}_group_analysis.pdf"

            # 新结构：PDF文件在result_folder中（已经是figures文件夹）
            pdf_path = os.path.join(self.result_folder, pdf_filename)

        if not os.path.exists(pdf_path):
            print(f"❌ 没有找到PDF文件: {pdf_path}")
            return None

        try:
            # 如果需要保存为其他格式
            if save_path:
                import shutil
                shutil.copy2(pdf_path, save_path)
                print(f"✅ PDF文件已复制到: {save_path}")

            # 显示PDF信息
            if show_image:
                print(f"📊 PDF图表文件: {os.path.basename(pdf_path)}")
                print(f"📁 文件路径: {pdf_path}")
                print(f"📏 文件大小: {os.path.getsize(pdf_path)} 字节")

                # 设置标题
                if self.evaluation_mode == 'multi_symbol':
                    title = f"分组分析图表 - {symbol} ({period})"
                else:
                    title = f"分组分析图表 - {self.metadata.get('factor_name', 'Unknown')}"

                print(f"📋 图表标题: {title}")
                print("💡 请使用PDF阅读器打开文件查看图表")

            return pdf_path

        except Exception as e:
            print(f"❌ 处理PDF文件失败: {str(e)}")
            return None

    def get_performance_summary(self) -> pd.DataFrame:
        """获取性能摘要表格"""
        all_results = self.get_all_results()
        summary_data = []

        for period_or_mode, period_data in all_results.items():
            for symbol, results in period_data.items():
                symbol_name = results['symbol']

                # 提取关键指标
                stationarity = results['stationarity_test']
                correlation = results['correlation_analysis']
                ir_result = results['information_ratio']

                row = {
                    'Symbol': symbol_name,
                    'Is_Stationary': stationarity.get('is_stationary', False) if stationarity else False,
                    'P_Value': stationarity.get('p_value', None) if stationarity else None,
                    'IC': correlation.get('IC', None) if correlation else None,
                    'Rank_IC': correlation.get('Rank_IC', None) if correlation else None,
                    'IR': ir_result.get('IR', None) if ir_result else None,
                }

                # 添加分组收益信息
                group_analysis = results['group_analysis']
                if group_analysis and 'group_stats' in group_analysis:
                    group_stats = group_analysis['group_stats']
                    if 'return_mean' in group_stats:
                        returns = list(group_stats['return_mean'].values())
                        if returns:
                            row.update({
                                'Min_Return': min(returns),
                                'Max_Return': max(returns),
                                'Return_Spread': max(returns) - min(returns)
                            })

                summary_data.append(row)

        return pd.DataFrame(summary_data)

    def print_performance_table(self):
        """打印性能摘要表格"""
        print("\n" + "=" * 80)
        print("📋 性能摘要表格")
        print("=" * 80)

        df = self.get_performance_summary()
        if not df.empty:
            # 设置显示选项
            pd.set_option('display.max_columns', None)
            pd.set_option('display.width', None)
            pd.set_option('display.float_format', '{:.6f}'.format)

            print(df.to_string(index=False))
        else:
            print("❌ 没有可用的性能数据")

    def compare_symbols(self, period: str = None) -> pd.DataFrame:
        """
        对比不同币种的表现（多币种模式）

        参数:
        period: 指定时间周期，如果为None则对比所有周期

        返回:
        pd.DataFrame: 对比结果表格
        """
        if self.evaluation_mode != 'multi_symbol':
            print("❌ 此功能仅适用于多币种模式")
            return pd.DataFrame()

        comparison_data = []
        periods_to_compare = [
            period] if period else self.evaluation_results.keys()

        for p in periods_to_compare:
            if p not in self.evaluation_results:
                continue

            period_results = self.evaluation_results[p]
            for symbol, results in period_results.items():
                if 'error' in results:
                    continue

                # 提取关键指标
                stationarity = results.get('stationarity_test', {})
                correlation = results.get('correlation_analysis', {})
                ir_result = results.get('information_ratio', {})
                group_analysis = results.get('group_analysis', {})

                row = {
                    'Period': p,
                    'Symbol': symbol,
                    'Is_Stationary': stationarity.get('is_stationary', False),
                    'P_Value': stationarity.get('p_value', None),
                    'IC': correlation.get('IC', None),
                    'Rank_IC': correlation.get('Rank_IC', None),
                    'IR': ir_result.get('IR', None),
                }

                # 添加分组收益信息
                if 'group_stats' in group_analysis:
                    group_stats = group_analysis['group_stats']
                    if 'return_mean' in group_stats:
                        returns = list(group_stats['return_mean'].values())
                        if returns:
                            row.update({
                                'Min_Return': min(returns),
                                'Max_Return': max(returns),
                                'Return_Spread': max(returns) - min(returns),
                                'Avg_Return': sum(returns) / len(returns)
                            })

                comparison_data.append(row)

        return pd.DataFrame(comparison_data)

    def print_comparison_table(self, period: str = None, sort_by: str = 'IC'):
        """
        打印币种对比表格

        参数:
        period: 指定时间周期
        sort_by: 排序字段
        """
        print("\n" + "=" * 80)
        print("🔄 币种表现对比")
        print("=" * 80)

        df = self.compare_symbols(period)
        if not df.empty:
            # 按指定字段排序
            if sort_by in df.columns:
                df = df.sort_values(by=sort_by, ascending=False)

            # 设置显示选项
            pd.set_option('display.max_columns', None)
            pd.set_option('display.width', None)
            pd.set_option('display.float_format', '{:.6f}'.format)

            print(f"排序字段: {sort_by}")
            print(df.to_string(index=False))
        else:
            print("❌ 没有可用的对比数据")

    def get_best_performers(self, metric: str = 'IC', top_n: int = 3) -> pd.DataFrame:
        """
        获取表现最佳的币种

        参数:
        metric: 评估指标 ('IC', 'Rank_IC', 'IR', 'Return_Spread')
        top_n: 返回前N个

        返回:
        pd.DataFrame: 最佳表现者
        """
        if self.evaluation_mode != 'multi_symbol':
            print("❌ 此功能仅适用于多币种模式")
            return pd.DataFrame()

        df = self.compare_symbols()
        if df.empty or metric not in df.columns:
            return pd.DataFrame()

        # 按指标排序并取前N个
        df_sorted = df.sort_values(by=metric, ascending=False)
        return df_sorted.head(top_n)

    def print_best_performers(self, metric: str = 'IC', top_n: int = 3):
        """打印表现最佳的币种"""
        print(f"\n🏆 {metric} 指标表现最佳的 {top_n} 个币种:")
        print("-" * 60)

        df_best = self.get_best_performers(metric, top_n)
        if not df_best.empty:
            for i, (_, row) in enumerate(df_best.iterrows(), 1):
                symbol = row['Symbol']
                period = row['Period']
                value = row[metric]
                print(f"{i}. {symbol} ({period}): {metric} = {value:.6f}")
        else:
            print("❌ 没有可用数据")

    def show_all_images(self):
        """显示所有图片（直接显示已生成的图形对象）"""
        print("\n" + "=" * 80)
        print("🖼️  显示所有图片")
        print("=" * 80)

        try:
            self.plot_saved_figures()
        except Exception as e:
            print(f"❌ 显示图片失败: {str(e)}")

    def plot_saved_figures(self):
        """
        显示已保存的图形对象
        """
        import matplotlib.pyplot as plt

        figures_found = []

        if self.evaluation_mode == 'multi_symbol':
            # 多币种模式：显示所有币种的图片
            for period, period_results in self.evaluation_results.items():
                for symbol, results in period_results.items():
                    if 'error' in results:
                        continue

                    # 1. 分组分析图片
                    if 'group_analysis' in results:
                        group_analysis = results['group_analysis']
                        if group_analysis.get('figure_info') == 'Figure saved as separate PDF file':
                            title = f"{symbol} ({period}) - 分组分析"
                            figures_found.append(
                                (title, 'group_analysis', period, symbol))

                    # 2. 因子分布图片
                    if 'factor_distribution' in results:
                        factor_dist = results['factor_distribution']
                        if factor_dist.get('figure_info') == 'Figure saved as separate PDF file':
                            title = f"{symbol} ({period}) - 因子分布"
                            figures_found.append(
                                (title, 'distribution', period, symbol))
        else:
            # 单币种模式：显示单个币种的图片
            factor_name = self.metadata.get('factor_name', 'Unknown')

            # 1. 分组分析图片
            if 'group_analysis' in self.evaluation_results:
                group_analysis = self.evaluation_results['group_analysis']
                if group_analysis.get('figure_info') == 'Figure saved as separate PDF file':
                    title = f"{factor_name} - 分组分析"
                    figures_found.append((title, 'group_analysis', None, None))

            # 2. 因子分布图片
            if 'factor_distribution' in self.evaluation_results:
                factor_dist = self.evaluation_results['factor_distribution']
                if factor_dist.get('figure_info') == 'Figure saved as separate PDF file':
                    title = f"{factor_name} - 因子分布"
                    figures_found.append((title, 'distribution', None, None))

        if not figures_found:
            print("❌ 没有找到已保存的图片")
            print("💡 请确保已运行评估并保存了图片")
            return

        print(f"📊 找到 {len(figures_found)} 个已保存的图片")

        # 显示提示信息
        print("💡 图片已保存为PDF文件，位置:")

        for title, figure_type, period, symbol in figures_found:
            if self.evaluation_mode == 'multi_symbol':
                factor_name = self.metadata.get('factor_name', 'unknown')
                if figure_type == 'group_analysis':
                    pdf_name = f"{factor_name}_{period}_{symbol}_group_analysis.pdf"
                else:
                    pdf_name = f"{factor_name}_{period}_{symbol}_distribution.pdf"
            else:
                factor_name = self.metadata.get('factor_name', 'unknown')
                if figure_type == 'group_analysis':
                    pdf_name = f"{factor_name}_group_analysis.pdf"
                else:
                    pdf_name = f"{factor_name}_distribution.pdf"

            # 新结构：PDF文件在result_folder中（已经是figures文件夹）
            pdf_path = os.path.join(self.result_folder, pdf_name)

            print(f"   📊 {title}")
            print(f"      📁 {pdf_path}")

        print(f"\n💡 使用方法:")
        print(f"1. 📂 打开文件夹: {self.result_folder}")
        print(f"2. 📊 双击PDF文件查看图片")
        print(f"3. 🖼️  或使用PDF阅读器打开")

    def plot_all_figures(self, figsize_per_plot=(10, 6)):
        """
        重新生成并显示所有图片

        参数:
        figsize_per_plot: tuple, 每个图片的大小
        """
        import matplotlib.pyplot as plt

        figures_info = self._collect_figures_info()

        if not figures_info:
            print("❌ 没有找到图片信息")
            return

        print(f"📊 找到 {len(figures_info)} 个图片")

        # 逐个显示图片
        for i, (title, figure_type, data_info) in enumerate(figures_info, 1):
            print(f"\n📊 显示图片 {i}/{len(figures_info)}: {title}")

            try:
                if figure_type == 'group_analysis':
                    self._plot_group_analysis_figure(
                        title, data_info, figsize_per_plot)
                elif figure_type == 'distribution':
                    self._plot_distribution_figure(
                        title, data_info, figsize_per_plot)
                else:
                    print(f"⚠️  未知图片类型: {figure_type}")

            except Exception as e:
                print(f"❌ 显示图片失败: {title}, 错误: {str(e)}")

        print(f"\n✅ 完成显示 {len(figures_info)} 个图片")

    def _collect_figures_info(self):
        """收集所有图片信息"""
        figures_info = []

        if self.evaluation_mode == 'multi_symbol':
            # 多币种模式
            for period, period_results in self.evaluation_results.items():
                for symbol, results in period_results.items():
                    if 'error' in results:
                        continue

                    # 分组分析图片
                    if 'group_analysis' in results:
                        group_analysis = results['group_analysis']
                        if group_analysis.get('figure_info') == 'Figure saved as separate PDF file':
                            title = f"{symbol} ({period}) - 分组分析"
                            figures_info.append((title, 'group_analysis', {
                                'period': period,
                                'symbol': symbol,
                                'group_stats': group_analysis.get('group_stats', {})
                            }))

                    # 因子分布图片
                    if 'factor_distribution' in results:
                        factor_dist = results['factor_distribution']
                        if factor_dist.get('figure_info') == 'Figure saved as separate PDF file':
                            title = f"{symbol} ({period}) - 因子分布"
                            figures_info.append((title, 'distribution', {
                                'period': period,
                                'symbol': symbol,
                                'factor_data': factor_dist
                            }))
        else:
            # 单币种模式
            factor_name = self.metadata.get('factor_name', 'Unknown')

            # 分组分析图片
            if 'group_analysis' in self.evaluation_results:
                group_analysis = self.evaluation_results['group_analysis']
                if group_analysis.get('figure_info') == 'Figure saved as separate PDF file':
                    title = f"{factor_name} - 分组分析"
                    figures_info.append((title, 'group_analysis', {
                        'group_stats': group_analysis.get('group_stats', {})
                    }))

            # 因子分布图片
            if 'factor_distribution' in self.evaluation_results:
                factor_dist = self.evaluation_results['factor_distribution']
                if factor_dist.get('figure_info') == 'Figure saved as separate PDF file':
                    title = f"{factor_name} - 因子分布"
                    figures_info.append((title, 'distribution', {
                        'factor_data': factor_dist
                    }))

        return figures_info

    def _plot_group_analysis_figure(self, title, data_info, figsize):
        """绘制分组分析图片"""
        import matplotlib.pyplot as plt

        group_stats = data_info.get('group_stats', {})

        if not group_stats or 'return_mean' not in group_stats:
            print(f"⚠️  {title}: 缺少分组统计数据")
            return

        # 提取数据
        groups = list(group_stats['return_mean'].keys())
        returns = list(group_stats['return_mean'].values())

        # 创建图形
        fig, ax = plt.subplots(figsize=figsize)

        # 绘制柱状图
        bars = ax.bar(groups, returns, alpha=0.7, color='steelblue')

        # 添加数值标签
        for bar, ret in zip(bars, returns):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                    f'{ret:.4f}', ha='center', va='bottom' if height >= 0 else 'top')

        ax.set_title(title, fontsize=12, pad=20)
        ax.set_xlabel('分组')
        ax.set_ylabel('平均收益')
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

    def _plot_distribution_figure(self, title, data_info, figsize):
        """绘制因子分布图片"""
        import matplotlib.pyplot as plt

        factor_data = data_info.get('factor_data', {})

        # 这里我们只能显示统计信息，因为原始数据不在JSON中
        # 创建一个信息展示图
        fig, ax = plt.subplots(figsize=figsize)

        # 提取统计信息
        stats_text = []
        if 'mean' in factor_data:
            stats_text.append(f"均值: {factor_data['mean']:.6f}")
        if 'std' in factor_data:
            stats_text.append(f"标准差: {factor_data['std']:.6f}")
        if 'skewness' in factor_data:
            stats_text.append(f"偏度: {factor_data['skewness']:.6f}")
        if 'kurtosis' in factor_data:
            stats_text.append(f"峰度: {factor_data['kurtosis']:.6f}")

        if not stats_text:
            stats_text = ["因子分布统计信息", "（原始数据未保存在JSON中）"]

        # 显示统计信息
        ax.text(0.5, 0.5, '\n'.join(stats_text),
                ha='center', va='center', transform=ax.transAxes,
                fontsize=14, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))

        ax.set_title(title, fontsize=12, pad=20)
        ax.axis('off')

        plt.tight_layout()
        plt.show()

        print(f"💡 注意: 因子分布图只显示统计信息，原始数据未保存在JSON中")

    def _find_all_pdf_files(self):
        """查找结果文件夹中的所有PDF文件"""
        pdf_files = {}

        if not self.result_folder or not os.path.exists(self.result_folder):
            return pdf_files

        for file in os.listdir(self.result_folder):
            if file.endswith('.pdf'):
                pdf_path = os.path.join(self.result_folder, file)
                pdf_files[file] = pdf_path

        return pdf_files

    def _generate_pdf_title(self, pdf_filename):
        """根据PDF文件名生成标题"""
        # 移除.pdf扩展名
        name = pdf_filename.replace('.pdf', '')

        # 解析文件名
        if 'group_analysis' in name:
            if self.evaluation_mode == 'multi_symbol':
                # 多币种: factor_period_symbol_group_analysis.pdf
                parts = name.split('_')
                if len(parts) >= 4:
                    symbol = parts[-3]
                    period = parts[-4]
                    return f"{symbol} ({period})\n分组分析"
            else:
                # 单币种: factor_group_analysis.pdf
                return "分组分析"
        elif 'distribution' in name:
            if self.evaluation_mode == 'multi_symbol':
                # 多币种: factor_period_symbol_distribution.pdf
                parts = name.split('_')
                if len(parts) >= 3:
                    symbol = parts[-2]
                    period = parts[-3]
                    return f"{symbol} ({period})\n因子分布"
            else:
                # 单币种: factor_distribution.pdf
                return "因子分布"
        else:
            return name

    def export_results(self, output_dir: str = "exported_results"):
        """
        导出结果到文件

        参数:
        output_dir: 输出目录
        """
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        base_name = os.path.splitext(os.path.basename(self.json_file_path))[0]

        # 1. 导出性能摘要表格
        df_summary = self.get_performance_summary()
        if not df_summary.empty:
            summary_path = os.path.join(output_dir, f"{base_name}_summary.csv")
            df_summary.to_csv(summary_path, index=False)
            print(f"✅ 性能摘要已导出: {summary_path}")

        # 2. 导出对比表格（多币种模式）
        if self.evaluation_mode == 'multi_symbol':
            df_comparison = self.compare_symbols()
            if not df_comparison.empty:
                comparison_path = os.path.join(
                    output_dir, f"{base_name}_comparison.csv")
                df_comparison.to_csv(comparison_path, index=False)
                print(f"✅ 对比表格已导出: {comparison_path}")

        # 3. 导出图片
        self._export_images(output_dir, base_name)

    def _export_images(self, output_dir: str, base_name: str):
        """导出所有图片"""
        if self.evaluation_mode == 'multi_symbol':
            # 多币种模式：导出所有币种的图片
            for period, period_results in self.evaluation_results.items():
                for symbol, results in period_results.items():
                    if 'error' in results:
                        continue

                    group_analysis = results.get('group_analysis', {})
                    if 'figure_base64' in group_analysis:
                        image_name = f"{base_name}_{period}_{symbol}_chart.png"
                        image_path = os.path.join(output_dir, image_name)

                        saved_path = self.extract_and_show_image(
                            period=period,
                            symbol=symbol,
                            save_path=image_path,
                            show_image=False
                        )
                        if saved_path:
                            print(f"✅ 图片已导出: {image_name}")
        else:
            # 单币种模式：导出单个图片
            group_analysis = self.evaluation_results.get('group_analysis', {})
            if 'figure_base64' in group_analysis:
                image_name = f"{base_name}_chart.png"
                image_path = os.path.join(output_dir, image_name)

                saved_path = self.extract_and_show_image(
                    save_path=image_path,
                    show_image=False
                )
                if saved_path:
                    print(f"✅ 图片已导出: {image_name}")


if __name__ == '__main__':

    # 使用DataService获取数据的示例
    ds = DataService()
    currency = 'ETHUSDT_15m_2020_2025'
    df = ds[currency]

    def ma_deviation_factor(df, ma_period=20):
        ma = df['close'].rolling(ma_period).mean()
        return (df['close'])

    evaluator = FactorEvaluation(
        # df,
        time_periods=['1h'],
        future_return_periods=10,
        factor_func=ma_deviation_factor,
        factor_name='ma_deviation_factor')
    results = evaluator.run_full_evaluation()

    # # 传入带参数的因子函数
    # evaluator4 = FactorEvaluation(
    #     # df,
    #     time_periods=['1h'],
    #     future_return_periods=10,
    #     factor_func=ma_deviation_factor,
    #     factor_name=ma_deviation_factor.__name__)
    # results4 = evaluator4.run_full_evaluation()
