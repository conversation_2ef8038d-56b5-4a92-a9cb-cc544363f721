import pandas as pd


class HideFactors:
    """
    静态因子计算方法库
    所有方法命名规则: hide{3位序号}
    计算方法: close_shift_i / close_shift_j (i和j定义见方法docstring)
    """

    # ------------------------- i=0 的因子 -------------------------
    @staticmethod
    def hide001(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_1 (i=0, j=1)"""
        return (df['close'].shift(0) / df['close'].shift(1)).fillna(1)

    @staticmethod
    def hide002(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_3 (i=0, j=3)"""
        return (df['close'].shift(0) / df['close'].shift(3)).fillna(1)

    @staticmethod
    def hide003(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_5 (i=0, j=5)"""
        return (df['close'].shift(0) / df['close'].shift(5)).fillna(1)

    @staticmethod
    def hide004(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_10 (i=0, j=10)"""
        return (df['close'].shift(0) / df['close'].shift(10)).fillna(1)

    @staticmethod
    def hide005(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_20 (i=0, j=20)"""
        return (df['close'].shift(0) / df['close'].shift(20)).fillna(1)

    @staticmethod
    def hide006(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_30 (i=0, j=30)"""
        return (df['close'].shift(0) / df['close'].shift(30)).fillna(1)

    @staticmethod
    def hide007(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_60 (i=0, j=60)"""
        return (df['close'].shift(0) / df['close'].shift(60)).fillna(1)

    # ------------------------- i=1 的因子 (跳过 j=1) -------------------------
    @staticmethod
    def hide008(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_3 (i=1, j=3)"""
        return (df['close'].shift(1) / df['close'].shift(3)).fillna(1)

    @staticmethod
    def hide009(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_5 (i=1, j=5)"""
        return (df['close'].shift(1) / df['close'].shift(5)).fillna(1)

    @staticmethod
    def hide010(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_10 (i=1, j=10)"""
        return (df['close'].shift(1) / df['close'].shift(10)).fillna(1)

    @staticmethod
    def hide011(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_20 (i=1, j=20)"""
        return (df['close'].shift(1) / df['close'].shift(20)).fillna(1)

    @staticmethod
    def hide012(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_30 (i=1, j=30)"""
        return (df['close'].shift(1) / df['close'].shift(30)).fillna(1)

    @staticmethod
    def hide013(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_60 (i=1, j=60)"""
        return (df['close'].shift(1) / df['close'].shift(60)).fillna(1)

    # ------------------------- i=3 的因子 (跳过 j=3) -------------------------
    @staticmethod
    def hide014(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_1 (i=3, j=1)"""
        return (df['close'].shift(3) / df['close'].shift(1)).fillna(1)

    @staticmethod
    def hide015(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_5 (i=3, j=5)"""
        return (df['close'].shift(3) / df['close'].shift(5)).fillna(1)

    @staticmethod
    def hide016(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_10 (i=3, j=10)"""
        return (df['close'].shift(3) / df['close'].shift(10)).fillna(1)

    @staticmethod
    def hide017(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_20 (i=3, j=20)"""
        return (df['close'].shift(3) / df['close'].shift(20)).fillna(1)

    @staticmethod
    def hide018(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_30 (i=3, j=30)"""
        return (df['close'].shift(3) / df['close'].shift(30)).fillna(1)

    @staticmethod
    def hide019(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_60 (i=3, j=60)"""
        return (df['close'].shift(3) / df['close'].shift(60)).fillna(1)

    # ------------------------- i=5 的因子 (跳过 j=5) -------------------------
    @staticmethod
    def hide020(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_1 (i=5, j=1)"""
        return (df['close'].shift(5) / df['close'].shift(1)).fillna(1)

    @staticmethod
    def hide021(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_3 (i=5, j=3)"""
        return (df['close'].shift(5) / df['close'].shift(3)).fillna(1)

    @staticmethod
    def hide022(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_10 (i=5, j=10)"""
        return (df['close'].shift(5) / df['close'].shift(10)).fillna(1)

    @staticmethod
    def hide023(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_20 (i=5, j=20)"""
        return (df['close'].shift(5) / df['close'].shift(20)).fillna(1)

    @staticmethod
    def hide024(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_30 (i=5, j=30)"""
        return (df['close'].shift(5) / df['close'].shift(30)).fillna(1)

    @staticmethod
    def hide025(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_60 (i=5, j=60)"""
        return (df['close'].shift(5) / df['close'].shift(60)).fillna(1)

    # ------------------------- i=15 的因子 -------------------------
    @staticmethod
    def hide026(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_1 (i=15, j=1)"""
        return (df['close'].shift(15) / df['close'].shift(1)).fillna(1)

    @staticmethod
    def hide027(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_3 (i=15, j=3)"""
        return (df['close'].shift(15) / df['close'].shift(3)).fillna(1)

    @staticmethod
    def hide028(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_5 (i=15, j=5)"""
        return (df['close'].shift(15) / df['close'].shift(5)).fillna(1)

    @staticmethod
    def hide029(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_10 (i=15, j=10)"""
        return (df['close'].shift(15) / df['close'].shift(10)).fillna(1)

    @staticmethod
    def hide030(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_20 (i=15, j=20)"""
        return (df['close'].shift(15) / df['close'].shift(20)).fillna(1)

    @staticmethod
    def hide031(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_30 (i=15, j=30)"""
        return (df['close'].shift(15) / df['close'].shift(30)).fillna(1)

    @staticmethod
    def hide032(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_60 (i=15, j=60)"""
        return (df['close'].shift(15) / df['close'].shift(60)).fillna(1)

    # ==============(新评估框架下产生的因子)==========================================#

    @staticmethod
    def c_hide_001(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_1 (i=0, j=1)"""
        return abs((df['close'].shift(0) / df['close'].shift(1)).fillna(1) - 1)

    @staticmethod
    def c_hide_002(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_3 (i=0, j=3)"""
        return abs((df['close'].shift(0) / df['close'].shift(3)).fillna(1) - 1)

    @staticmethod
    def c_hide_003(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_5 (i=0, j=5)"""
        return abs((df['close'].shift(0) / df['close'].shift(5)).fillna(1) - 1)

    @staticmethod
    def c_hide_004(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_10 (i=0, j=10)"""
        return abs((df['close'].shift(0) / df['close'].shift(10)).fillna(1) - 1)

    @staticmethod
    def c_hide_005(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_20 (i=0, j=20)"""
        return abs((df['close'].shift(0) / df['close'].shift(20)).fillna(1) - 1)

    @staticmethod
    def c_hide_006(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_30 (i=0, j=30)"""
        return abs((df['close'].shift(0) / df['close'].shift(30)).fillna(1) - 1)

    @staticmethod
    def c_hide_007(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_60 (i=0, j=60)"""
        return abs((df['close'].shift(0) / df['close'].shift(60)).fillna(1) - 1)

    # ------------------------- i=1 的因子 (跳过 j=1) -------------------------
    @staticmethod
    def c_hide_008(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_3 (i=1, j=3)"""
        return abs((df['close'].shift(1) / df['close'].shift(3)).fillna(1) - 1)

    @staticmethod
    def c_hide_009(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_5 (i=1, j=5)"""
        return abs((df['close'].shift(1) / df['close'].shift(5)).fillna(1) - 1)

    @staticmethod
    def c_hide_010(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_10 (i=1, j=10)"""
        return abs((df['close'].shift(1) / df['close'].shift(10)).fillna(1) - 1)

    @staticmethod
    def c_hide_011(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_20 (i=1, j=20)"""
        return abs((df['close'].shift(1) / df['close'].shift(20)).fillna(1) - 1)

    @staticmethod
    def c_hide_012(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_30 (i=1, j=30)"""
        return abs((df['close'].shift(1) / df['close'].shift(30)).fillna(1) - 1)

    @staticmethod
    def c_hide_013(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_60 (i=1, j=60)"""
        return abs((df['close'].shift(1) / df['close'].shift(60)).fillna(1) - 1)

    # ------------------------- i=3 的因子 (跳过 j=3) -------------------------
    @staticmethod
    def c_hide_014(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_1 (i=3, j=1)"""
        return abs((df['close'].shift(3) / df['close'].shift(1)).fillna(1) - 1)

    @staticmethod
    def c_hide_015(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_5 (i=3, j=5)"""
        return abs((df['close'].shift(3) / df['close'].shift(5)).fillna(1) - 1)

    @staticmethod
    def c_hide_016(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_10 (i=3, j=10)"""
        return abs((df['close'].shift(3) / df['close'].shift(10)).fillna(1) - 1)

    @staticmethod
    def c_hide_017(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_20 (i=3, j=20)"""
        return abs((df['close'].shift(3) / df['close'].shift(20)).fillna(1) - 1)

    @staticmethod
    def c_hide_018(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_30 (i=3, j=30)"""
        return abs((df['close'].shift(3) / df['close'].shift(30)).fillna(1) - 1)

    @staticmethod
    def c_hide_019(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_60 (i=3, j=60)"""
        return abs((df['close'].shift(3) / df['close'].shift(60)).fillna(1) - 1)

    # ------------------------- i=5 的因子 (跳过 j=5) -------------------------
    @staticmethod
    def c_hide_020(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_1 (i=5, j=1)"""
        return abs((df['close'].shift(5) / df['close'].shift(1)).fillna(1) - 1)

    @staticmethod
    def c_hide_021(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_3 (i=5, j=3)"""
        return abs((df['close'].shift(5) / df['close'].shift(3)).fillna(1) - 1)

    @staticmethod
    def c_hide_022(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_10 (i=5, j=10)"""
        return abs((df['close'].shift(5) / df['close'].shift(10)).fillna(1) - 1)

    @staticmethod
    def c_hide_023(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_20 (i=5, j=20)"""
        return abs((df['close'].shift(5) / df['close'].shift(20)).fillna(1) - 1)

    @staticmethod
    def c_hide_024(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_30 (i=5, j=30)"""
        return abs((df['close'].shift(5) / df['close'].shift(30)).fillna(1) - 1)

    @staticmethod
    def c_hide_025(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_60 (i=5, j=60)"""
        return abs((df['close'].shift(5) / df['close'].shift(60)).fillna(1) - 1)

    # ------------------------- i=15 的因子 -------------------------
    @staticmethod
    def c_hide_026(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_1 (i=15, j=1)"""
        return abs((df['close'].shift(15) / df['close'].shift(1)).fillna(1) - 1)

    @staticmethod
    def c_hide_027(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_3 (i=15, j=3)"""
        return abs((df['close'].shift(15) / df['close'].shift(3)).fillna(1) - 1)

    @staticmethod
    def c_hide_028(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_5 (i=15, j=5)"""
        return abs((df['close'].shift(15) / df['close'].shift(5)).fillna(1) - 1)

    @staticmethod
    def c_hide_029(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_10 (i=15, j=10)"""
        return abs((df['close'].shift(15) / df['close'].shift(10)).fillna(1) - 1)

    @staticmethod
    def c_hide_030(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_20 (i=15, j=20)"""
        return abs((df['close'].shift(15) / df['close'].shift(20)).fillna(1) - 1)

    @staticmethod
    def c_hide_031(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_30 (i=15, j=30)"""
        return abs((df['close'].shift(15) / df['close'].shift(30)).fillna(1) - 1)

    @staticmethod
    def c_hide_032(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_60 (i=15, j=60)"""
        return abs((df['close'].shift(15) / df['close'].shift(60)).fillna(1) - 1)
