"""
测试优化后的因子分层收益计算代码

这个脚本展示了如何使用优化后的函数，并提供了性能对比
"""

import pandas as pd
import numpy as np
import time
from temp2 import (
    dynamic_quantile_weighted_returns_optimized,
    plot_quantile_returns,
    performance_comparison,
    validate_results
)

def generate_test_data(n_dates=252, n_assets=1000, seed=42):
    """
    生成测试用的因子和收益数据
    
    :param n_dates: 时间点数量
    :param n_assets: 资产数量  
    :param seed: 随机种子
    :return: factor_df, ret_df
    """
    np.random.seed(seed)
    
    # 生成日期索引
    dates = pd.date_range('2023-01-01', periods=n_dates, freq='D')
    
    # 生成资产代码
    assets = [f'Asset_{i:04d}' for i in range(n_assets)]
    
    # 生成因子数据（带有一些趋势和噪声）
    factor_data = np.random.randn(n_dates, n_assets)
    # 添加一些持续性
    for i in range(1, n_dates):
        factor_data[i] = 0.7 * factor_data[i-1] + 0.3 * factor_data[i]
    
    # 随机设置一些NaN值
    nan_mask = np.random.random((n_dates, n_assets)) < 0.05
    factor_data[nan_mask] = np.nan
    
    # 生成收益数据（与因子有一定相关性）
    ret_data = np.random.randn(n_dates, n_assets) * 0.02
    # 添加因子暴露
    factor_exposure = np.nan_to_num(factor_data) * 0.001
    ret_data += factor_exposure
    
    # 随机设置一些NaN值
    ret_nan_mask = np.random.random((n_dates, n_assets)) < 0.03
    ret_data[ret_nan_mask] = np.nan
    
    factor_df = pd.DataFrame(factor_data, index=dates, columns=assets)
    ret_df = pd.DataFrame(ret_data, index=dates, columns=assets)
    
    return factor_df, ret_df

def main():
    """主测试函数"""
    print("=== 优化后的因子分层收益计算测试 ===\n")
    
    # 1. 生成测试数据
    print("1. 生成测试数据...")
    factor_df, ret_df = generate_test_data(n_dates=252, n_assets=500)
    print(f"   数据规模: {factor_df.shape[0]} 个交易日, {factor_df.shape[1]} 个资产")
    print(f"   因子数据缺失率: {factor_df.isna().sum().sum() / factor_df.size * 100:.2f}%")
    print(f"   收益数据缺失率: {ret_df.isna().sum().sum() / ret_df.size * 100:.2f}%\n")
    
    # 2. 运行优化版本
    print("2. 运行优化版本...")
    start_time = time.time()
    quantile_rets = dynamic_quantile_weighted_returns_optimized(factor_df, ret_df, n_groups=5)
    end_time = time.time()
    print(f"   执行时间: {end_time - start_time:.4f} 秒")
    print(f"   结果形状: {quantile_rets.shape}")
    print(f"   列名: {list(quantile_rets.columns)}\n")
    
    # 3. 验证结果
    print("3. 验证结果...")
    validate_results(factor_df, ret_df, quantile_rets, n_groups=5)
    print()
    
    # 4. 显示收益统计
    print("4. 分层收益统计:")
    print("   年化收益率 (假设252个交易日):")
    annual_returns = quantile_rets.mean() * 252
    for col in quantile_rets.columns:
        print(f"   {col}: {annual_returns[col]:.4f}")
    
    print(f"\n   年化波动率:")
    annual_vol = quantile_rets.std() * np.sqrt(252)
    for col in quantile_rets.columns:
        print(f"   {col}: {annual_vol[col]:.4f}")
    
    print(f"\n   夏普比率:")
    sharpe_ratio = annual_returns / annual_vol
    for col in quantile_rets.columns:
        print(f"   {col}: {sharpe_ratio[col]:.4f}")
    
    # 5. 绘制累积收益图
    print("\n5. 绘制累积收益图...")
    plot_quantile_returns(quantile_rets, "优化版本 - 因子分层累积收益")
    
    # 6. 性能对比（如果数据不太大的话）
    if factor_df.shape[0] * factor_df.shape[1] < 50000:  # 避免在大数据集上运行太久
        print("\n6. 性能对比测试...")
        performance_comparison(factor_df, ret_df, n_groups=5, n_runs=3)
    else:
        print("\n6. 数据集较大，跳过性能对比测试")
    
    print("\n=== 测试完成 ===")
    return quantile_rets

if __name__ == "__main__":
    result = main()
