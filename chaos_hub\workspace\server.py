from http.server import BaseHTTPRequestHandler, HTTPServer
from urllib.parse import urlparse, parse_qs
import json
import traceback
import numpy as np
import pandas as pd
from sqlalchemy import create_engine


MYSQL_CONFIG = {
    "host": "**************:33306",
    "user": "root",
    "password": "1234",
    "database": "ctadata"
}


class DataManager():
    def __init__(self):
        # self.__tim__ = ticker.TickerInfoManager()
        self.problem_ticker = []
        self.engine = create_engine(
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}/{MYSQL_CONFIG['database']}")

        return

    def concat_data(self, contract):
        """读取并合并单品类永续合约的K线数据"""
        query = f"SELECT * FROM {contract}"
        df = pd.read_sql(query, self.engine)
        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
        df.set_index('open_time', inplace=True)

        return df


class FactorDispatcher:
    """因子调度器，包含允许调用的安全方法"""

    @staticmethod
    def factor_sample_1(d):
        """示例因子1"""
        c = d.close
        long_T = 20
        short_T = 5
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        s = (
            (ma_short > ma_long)
        ).astype(int)

        return s

    @staticmethod
    def factor_sample_2(d):
        """示例因子2"""
        c = d.close
        long_T = 30
        short_T = 10
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        s = (
            (ma_short < ma_long)
        ).astype(int)

        return s


class RequestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        try:
            query = parse_qs(urlparse(self.path).query)
            self.validate_parameters(query)

            method_name = query['method'][0]
            contract_name = query['table'][0]
            # data_range = self.parse_range(query['range'][0])

            # 动态方法调用
            result = self.call_method(method_name, contract_name)

            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(result.to_json().encode())

        except Exception as e:
            self.handle_error(e)

    def validate_parameters(self, query):
        """参数验证"""
        required = ['method', 'table']
        missing = [p for p in required if p not in query]
        if missing:
            raise ValueError(f"缺少必要参数: {', '.join(missing)}")

    # def parse_range(self, range_str):
    #     """解析负数切片范围"""
    #     try:
    #         start, end = map(int, range_str.split(':'))
    #         return slice(start, None if end == 0 else end)
    #     except:
    #         raise ValueError("无效的范围格式，应为 start:end 格式的整数")

    def call_method(self, method_name, contract_name):
        """安全方法调用"""

        method = getattr(FactorDispatcher, method_name, None)
        if not method or not callable(method):
            raise AttributeError("方法不存在")

        # 获取数据
        data = DataManager().concat_data(contract_name)
        # handle_data = data[data_range.start:data_range.stop]

        return method(data)

    def handle_error(self, error):
        """统一错误处理"""
        error_msg = {
            "error": str(error),
            "traceback": traceback.format_exc()
        }

        status_code = 500
        if isinstance(error, ValueError):
            status_code = 400
        elif isinstance(error, (AttributeError, PermissionError)):
            status_code = 405

        self.send_response(status_code)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(error_msg).encode())


def run_server(port=6678):
    server = HTTPServer(('', port), RequestHandler)
    print(f"服务器运行在端口 {port}")
    server.serve_forever()


if __name__ == '__main__':
    run_server()
