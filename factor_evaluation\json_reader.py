import json
import os
import pandas as pd


class JsonReader:
    """
    简单的JSON评估结果读取器

    作用：读取JSON里的评估信息，然后可以选择是否保存到CSV
    """

    def __init__(self, json_path: str):
        """
        初始化

        参数:
        json_path: JSON文件路径
        """
        self.json_path = json_path
        self.data = None
        self.load_json()

    def load_json(self):
        """加载JSON文件"""
        try:
            with open(self.json_path, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            # print(f"✅ 加载成功: {os.path.basename(self.json_path)}")
        except Exception as e:
            raise ValueError(f"❌ 加载失败: {str(e)}")

    def get_evaluation_info(self):
        """获取评估信息"""
        if not self.data:
            return {}

        metadata = self.data.get('metadata', {})
        evaluation_results = self.data.get('evaluation_results', {})

        # 提取关键信息
        info = {
            '因子名称': metadata.get('factor_name', 'Unknown'),
            '时间戳': metadata.get('timestamp', 'Unknown'),
            '数据长度': metadata.get('data_length', 'Unknown'),
            '数据开始时间': metadata.get('data_start_time', 'Unknown'),
            '数据结束时间': metadata.get('data_end_time', 'Unknown')
        }

        # 如果是多币种模式，添加币种和周期信息
        if 'symbol' in metadata:
            info['币种'] = metadata.get('symbol', 'Unknown')
        if 'period' in metadata:
            info['时间周期'] = metadata.get('period', 'Unknown')

        # 提取评估指标
        stationarity = evaluation_results.get('stationarity_test', {})
        info['是否平稳'] = stationarity.get('is_stationary', None)
        info['平稳性p值'] = stationarity.get('p_value', None)

        correlation = evaluation_results.get('correlation_analysis', {})
        info['IC'] = correlation.get('IC', None)
        info['Rank_IC'] = correlation.get('Rank_IC', None)

        ir_result = evaluation_results.get('information_ratio', {})
        info['IR'] = ir_result.get('IR', None)

        # 分组收益统计
        group_analysis = evaluation_results.get('group_analysis', {})
        if 'group_stats' in group_analysis:
            group_stats = group_analysis['group_stats']
            if 'return_mean' in group_stats:
                return_means = list(group_stats['return_mean'].values())
                if return_means:
                    info['最高组收益'] = max(return_means)
                    info['最低组收益'] = min(return_means)
                    info['收益差'] = max(return_means) - min(return_means)

        # 因子分布统计
        factor_dist = evaluation_results.get('factor_distribution', {})
        if 'descriptive_stats' in factor_dist:
            desc_stats = factor_dist['descriptive_stats']
            info['因子均值'] = desc_stats.get('mean', None)
            info['因子标准差'] = desc_stats.get('std', None)

        return info

    def print_info(self):
        """打印评估信息"""
        info = self.get_evaluation_info()

        print("\n" + "="*50)
        print("📊 评估信息")
        print("="*50)

        for key, value in info.items():
            if value is not None:
                if isinstance(value, float):
                    print(f"{key}: {value:.6f}")
                else:
                    print(f"{key}: {value}")
            else:
                print(f"{key}: N/A")

    def save_to_csv(self, csv_path: str = None):
        """
        保存到CSV文件

        参数:
        csv_path: CSV文件路径，如果为None则自动生成文件名

        返回:
        str: 保存的CSV文件路径
        """
        info = self.get_evaluation_info()

        # 自动生成文件名
        if csv_path is None:
            factor_name = info.get('因子名称', 'unknown')
            timestamp = info.get('时间戳', '').replace(
                ':', '').replace(' ', '_').replace('-', '')
            csv_path = f"{factor_name}_{timestamp}.csv"

        # 转换为DataFrame
        data_list = []
        for key, value in info.items():
            data_list.append({'指标': key, '值': value})

        df = pd.DataFrame(data_list)
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')

        print(f"✅ 已保存到: {csv_path}")
        return csv_path


def batch_process_json_to_csv(base_dir: str = 'results/jsons', output_csv: str = 'output.csv'):
    """
    批量处理所有JSON文件并保存到一个CSV中

    参数:
    base_dir: JSON文件所在的基础目录
    output_csv: 输出的CSV文件名

    返回:
    str: 保存的CSV文件路径
    """
    all_results = []
    processed_count = 0
    error_count = 0

    print(f"🔍 开始批量处理 {base_dir} 目录下的JSON文件...")

    # 递归查找所有JSON文件
    for root, _, files in os.walk(base_dir):
        for file in files:
            if file.endswith('.json'):
                json_path = os.path.join(root, file)
                try:
                    # 创建读取器并获取信息
                    reader = JsonReader(json_path)
                    info = reader.get_evaluation_info()

                    # 添加文件路径信息
                    info['文件路径'] = json_path
                    info['文件名'] = file

                    all_results.append(info)
                    processed_count += 1
                    # print(f"✅ 处理成功: {file}")

                except Exception as e:
                    error_count += 1
                    print(f"❌ 处理失败: {file} - {str(e)}")

    if not all_results:
        print("❌ 没有找到可处理的JSON文件")
        return None

    # 转换为DataFrame并保存
    df = pd.DataFrame(all_results)
    df.to_csv(output_csv, index=False, encoding='utf-8-sig')

    # print(f"\n📊 批量处理完成:")
    print(f"✅ 成功处理: {processed_count} 个文件")
    print(f"❌ 处理失败: {error_count} 个文件")
    print(f"💾 结果已保存到: {output_csv}")

    return output_csv


# 使用示例
if __name__ == "__main__":
    results_dir = 'results/jsons'
    if os.path.exists(results_dir):
        print("🚀 批量处理Json:")
        batch_process_json_to_csv(results_dir, 'output.csv')
    else:
        print("❌ results/jsons 目录不存在，无法演示批量处理")
