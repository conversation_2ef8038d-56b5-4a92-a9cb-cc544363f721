from setuptools import setup, Extension
from Cython.Build import cythonize
import numpy

# 定义扩展模块
extensions = [
    Extension(
        "factor_evaluation_server",  # 模块名
        ["factor_evaluation_server.py"],  # 源文件
        include_dirs=[numpy.get_include()],  # 包含numpy头文件
        define_macros=[("NPY_NO_DEPRECATED_API", "NPY_1_7_API_VERSION")],
        language_level=3,  # Python 3
    )
]

# 编译配置
setup(
    name="factor_evaluation_server_cython",
    ext_modules=cythonize(
        extensions,
        compiler_directives={
            'language_level': 3,
            'boundscheck': False,  # 关闭边界检查以提高性能
            'wraparound': False,   # 关闭负索引检查
            'cdivision': True,     # 使用C除法
            'nonecheck': False,    # 关闭None检查
        }
    ),
    zip_safe=False,
)
