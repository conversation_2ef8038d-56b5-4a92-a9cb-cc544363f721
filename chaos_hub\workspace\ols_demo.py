import statsmodels.api as sm
import pickle
from scipy.stats import spearmanr, pearsonr
from sqlalchemy import create_engine
from factors_lib.chao_factors_lib import ChaoFactorLib
import inspect
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

MYSQL_CONFIG = {
    "host": "************",
    "user": "root",
    "password": "1234",
    "database": "ctadata"
}


class DataManager():
    def __init__(self):
        self.engine = create_engine(
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}/{MYSQL_CONFIG['database']}")

    def concat_data(self, data):
        """读取并合并单品类永续合约的K线数据"""
        query = f"SELECT * FROM {data.contract}"
        df = pd.read_sql(query, self.engine)
        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
        df.set_index('open_time', inplace=True)
        data.data = df


class Data():
    def __init__(self, contract):
        self.contract = contract


# 读取数据
currency = 'BTCUSDT_15m_2022_2025'
d = Data(currency)
dm = DataManager()
dm.concat_data(d)
df = d.data.copy()

###################################################################
# 遍历 ChaoFactorLib 中所有以 "c_chu" 开头的因子计算
for name, func in inspect.getmembers(ChaoFactorLib, inspect.isfunction):
    if name.startswith("c_chu"):  # 只选择 "c_chuXXX" 相关的因子
        df[name] = func(df)
###################################################################

future_window = 150

# 计算未来收益
df['future_return'] = df['close'].shift(-future_window)/df['close'] - 1
df = df.fillna(0)

# 数据集总长度
n = len(df)

# 数据集划分
train_size = int(0.6 * n)
val_size = int(0.2 * n)
test_size = n - train_size - val_size

train_data = df.iloc[:train_size]
val_data = df.iloc[train_size:train_size + val_size]
test_data = df.iloc[train_size + val_size:]

# 选择因子作为特征
features = [x for x in df.columns if x.startswith('c_')]
features.extend(['open', 'high', 'low', 'close', 'volume'])

# 提取特征和目标变量
X_train, y_train = train_data[features], train_data['future_return']
X_val, y_val = val_data[features], val_data['future_return']
X_test, y_test = test_data[features], test_data['future_return']

# 添加截距项
X_train_ols = sm.add_constant(X_train)
X_val_ols = sm.add_constant(X_val)
X_test_ols = sm.add_constant(X_test)

# 训练 OLS 模型
model = sm.OLS(y_train, X_train_ols)
results = model.fit()

# 输出回归系数
print(results.summary())

# 预测
y_train_pred = results.predict(X_train_ols)
y_val_pred = results.predict(X_val_ols)
y_test_pred = results.predict(X_test_ols)

# 计算 MSE
train_mse = np.mean((y_train - y_train_pred) ** 2)
val_mse = np.mean((y_val - y_val_pred) ** 2)
test_mse = np.mean((y_test - y_test_pred) ** 2)

# 计算 R²
train_r2 = results.rsquared
val_r2 = 1 - np.sum((y_val - y_val_pred) ** 2) / \
    np.sum((y_val - y_val.mean()) ** 2)
test_r2 = 1 - np.sum((y_test - y_test_pred) ** 2) / \
    np.sum((y_test - y_test.mean()) ** 2)

# 计算信息系数（IC & Rank IC）
ic = pearsonr(y_test, y_test_pred)[0]
rank_ic = spearmanr(y_test, y_test_pred)[0]

# 输出结果
print('======================================================')
print(f'Train MSE: {train_mse:.8f}')
print(f'Validation MSE: {val_mse:.8f}')
print(f'Test MSE: {test_mse:.8f}')
print(f'Train R²: {train_r2:.8f}')
print(f'Validation R²: {val_r2:.8f}')
print(f'Test R²: {test_r2:.8f}')
print(f'IC (Pearson Correlation): {ic:.6f}')
print(f'Rank IC (Spearman Correlation): {rank_ic:.6f}')
print('======================================================')

# 可视化真实 vs 预测
plt.figure(figsize=(12, 6))
plt.plot(y_test.values, label='True')
plt.plot(y_test_pred, label='Predicted')
plt.legend()
plt.title('True vs Predicted Future Returns (OLS)')
plt.show()

# 绘制系数重要性
coef_df = pd.DataFrame({'Feature': X_train_ols.columns,
                       'Coefficient': results.params})
coef_df = coef_df.sort_values(by='Coefficient', ascending=False)

plt.figure(figsize=(10, 5))
plt.barh(coef_df['Feature'][:10], coef_df['Coefficient']
         [:10], color='steelblue')
plt.xlabel('Coefficient Value')
plt.ylabel('Feature')
plt.title('Top 10 Feature Coefficients')
plt.gca().invert_yaxis()
plt.show()
