from sklearn.preprocessing import StandardScaler
import pickle
from scipy.stats import spearmanr, pearsonr
from lightgbm import log_evaluation, early_stopping
from sqlalchemy import create_engine
from factors_lib.chao_factors_lib import ChaoFactorLib
import lightgbm as lgb
from sklearn.metrics import mean_squared_error
import inspect
import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
import scipy.stats as stats

MYSQL_CONFIG = {
    "host": "************",
    "user": "root",
    "password": "1234",
    "database": "ctadata"
}


class DataManager():

    def __init__(self):
        # self.__tim__ = ticker.TickerInfoManager()

        self.problem_ticker = []
        self.engine = create_engine(f"mysql+pymysql://{MYSQL_CONFIG['user']}:{
                                    MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}/{MYSQL_CONFIG['database']}")
        return

    def concat_data(self, data):
        """读取并合并单品类永续合约的K线数据"""
        query = f"SELECT * FROM {data.contract}"
        df = pd.read_sql(query, self.engine)
        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
        df.set_index('open_time', inplace=True)
        # 将所有K线数据合并
        data.data = df
        return


class Data():

    def __init__(self, contract):
        self.contract = contract
        return


currency = 'BTCUSDT_15m_2020_2025'
d = Data(currency)
dm = DataManager()
dm.concat_data(d)
df = d.data.copy()
###################################################################
# 遍历 ChaoFactorLib 中所有以 "c_chu" 开头的函数
for name, func in inspect.getmembers(ChaoFactorLib, inspect.isfunction):
    if name.startswith("c_chu"):  # 只选择 "c_chuXXX" 相关的因子
        df[name] = func(df)
###################################################################

features = [x for x in df.columns if x.startswith('c_')]
# features.extend(['open', 'high', 'low', 'close', 'volume'])

# scaler = StandardScaler()
# df[features] = scaler.fit_transform(df[features])  # 只对因子列进行标准化

future_window = 150

# 计算未来收益
df['future_return'] = df['close'].shift(-future_window)/df['close'] - 1

df = df.fillna(0)

# 数据集总长度（此时已排除无效数据）
n = len(df)

# 划分比例（保持原始比例意图，但基于新数据集长度）
train_size = int(0.6 * n)        # 训练集占 60%
val_size = int(0.2 * n)          # 验证集占 20%
test_size = n - train_size - val_size  # 测试集占 20%

# 划分数据（确保时间序列顺序）
train_data = df.iloc[:train_size]
val_data = df.iloc[train_size:train_size + val_size]
test_data = df.iloc[train_size + val_size:]


# 提取特征和目标变量
X_train, y_train = train_data[features], train_data['future_return']
X_val, y_val = val_data[features], val_data['future_return']
X_test, y_test = test_data[features], test_data['future_return']

# 创建 LightGBM 数据集
train_data_lgb = lgb.Dataset(X_train, label=y_train)
val_data_lgb = lgb.Dataset(X_val, label=y_val, reference=train_data_lgb)

# 设置更合理的参数
params = {
    'objective': 'regression',
    'metric': 'rmse',  # RMSE 比 MSE 更直观
    'boosting_type': 'gbdt',
    'num_leaves': 60,  # 增加叶子节点，提高表达能力
    'max_depth': 3,  # 限制树深度，防止过拟合
    'learning_rate': 0.02,  # 降低学习率
    'num_iterations': 2000,  # 增加迭代次数
    'min_data_in_leaf': 30,  # 叶子节点最小样本数，减少过拟合
    'feature_fraction': 0.8,  # 降低特征相关性，提高泛化能力
    'bagging_fraction': 0.9,  # 采用更多样本
    'bagging_freq': 3,  # 更频繁进行 bagging
    'lambda_l1': 0.1,  # L1 正则化
    'lambda_l2': 1.0,  # L2 正则化
    'verbose': -1,
    'seed': 42,  # 保证可复现性
    'lambda_l1': 0.1,
    'lambda_l2': 0.1,
}


# 训练模型
num_round = 1000
callbacks = [log_evaluation(period=100), early_stopping(stopping_rounds=100)]
bst = lgb.train(params,
                train_data_lgb,
                num_boost_round=num_round,
                valid_sets=[val_data_lgb],
                # early_stopping_rounds=100,
                # verbose_eval=10,
                callbacks=callbacks)

# 使用最佳迭代次数进行预测
y_pred = bst.predict(X_test, num_iteration=bst.best_iteration)

# 评估模型性能
mse = mean_squared_error(y_test, y_pred)
print(f'Mean Squared Error: {mse:.4f}')

# 建议添加可视化评估
plt.figure(figsize=(12, 6))
plt.plot(y_test.values, label='True')
plt.plot(y_pred, label='Predicted')
plt.legend()
plt.title('True vs Predicted Future Returns')
plt.show(block=False)


# 计算训练集和验证集 MSE
y_train_pred = bst.predict(X_train, num_iteration=bst.best_iteration)
y_val_pred = bst.predict(X_val, num_iteration=bst.best_iteration)

train_mse = mean_squared_error(y_train, y_train_pred)
val_mse = mean_squared_error(y_val, y_val_pred)
test_mse = mean_squared_error(y_test, y_pred)
print('======================================================')
print(f'Train MSE: {train_mse:.8f}')
print(f'Validation MSE: {val_mse:.8f}')
print(f'Test MSE: {test_mse:.8f}')
print('======================================================')
# 计算 Pearson 相关系数（IC）
ic = pearsonr(y_test, y_pred)[0]

# 计算 Spearman 相关系数（Rank IC）
rank_ic = spearmanr(y_test, y_pred)[0]

print(f'IC (Pearson Correlation): {ic:.6f}')
print(f'Rank IC (Spearman Correlation): {rank_ic:.6f}')
print('======================================================')
# 获取特征重要性
feature_importance = pd.DataFrame({'Feature': X_train.columns,
                                   'Importance': bst.feature_importance()})
print(feature_importance)
feature_importance = feature_importance.sort_values(
    by='Importance', ascending=False)

# 绘制特征重要性条形图
plt.figure(figsize=(10, 5))
plt.barh(feature_importance['Feature'][:10],
         feature_importance['Importance'][:10], color='steelblue')
plt.xlabel('Importance')
plt.ylabel('Feature')
plt.title('Top 10 Feature Importance')
plt.gca().invert_yaxis()
plt.show(block=True)
