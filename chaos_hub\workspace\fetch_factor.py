import functools
import os
import pickle

import pandas as pd
import numpy as np
import plotly.graph_objects as go
import requests
import plotly.io as pio
import matplotlib.pyplot as plt
from plotly.subplots import make_subplots
from sqlalchemy import create_engine

MYSQL_CONFIG = {
    "host": "************",
    "user": "root",
    "password": "1234",
    "database": "ctadata"
}


class DataManager():

    def __init__(self):
        # self.__tim__ = ticker.TickerInfoManager()
        self.problem_ticker = []
        self.engine = create_engine(
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}/{MYSQL_CONFIG['database']}")

        self.data_dir = 'data/'  # 存储数据的目录

        return

    def concat_data(self, data):
        # 定义持久化文件名
        data_file = os.path.join(self.data_dir, f"{data.contract}.pkl")
        print(f"K线数据读取:{data.contract}")

        # 如果存在本地文件，则读取
        if os.path.exists(data_file):
            with open(data_file, 'rb') as f:
                df = pickle.load(f)
                df = df.dropna(subset=['close'])
            print(f"从本地加载k线数据数据->{data_file}")

            data.data = df
        else:
            """读取并合并单品类永续合约的K线数据"""
            query = f"SELECT * FROM {data.contract}"
            df = pd.read_sql(query, self.engine)
            df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
            df.set_index('open_time', inplace=True)
            df = df.dropna(subset=['close'])
            # 将所有K线数据合并
            data.data = df

            # 将获取的数据保存到本地
            with open(data_file, 'wb') as f:
                pickle.dump(df, f)
            print(f"从在线获取k线数据数据并保存本地->{data_file}")
            print("\n")

            return


class Data():

    def __init__(self, contract):
        self.contract = contract
        return


def fill_window(window=100):
    '''装饰器，对因子进行信号填充'''
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            signals = func(*args, **kwargs)  # 调用原函数获取 Series

            s_modified = pd.Series(0, index=signals.index)  # 初始化全0

            idx_1 = signals[signals == 1].index
            idx_neg1 = signals[signals == -1].index

            s_pos = pd.Series(0, index=signals.index)
            s_neg = pd.Series(0, index=signals.index)

            for i in idx_1:
                s_pos.iloc[i:i + window] = 1  # 让后续100个点变成 1
            for i in idx_neg1:
                s_neg.iloc[i:i + window] = -1  # 让后续100个点变成 -1

            s_modified = s_pos + s_neg
            return s_modified
        return wrapper
    return decorator


class EventAnal():

    def __init__(self):
        return

    def get_data(self, d):
        global settings

        td = d.data
        return td.iloc[settings["from_data"]:None if settings["to_data"] == 0 else settings["to_data"]].reset_index(drop=True)

    def find_ticker_event(self, d, sig, flt, sign, is_absolute=False):
        global settings

        """查找事件并计算收益"""
        td = d.data.iloc[settings["from_data"]:None if settings["to_data"]
                         == 0 else settings["to_data"]].reset_index(drop=True)
        s = sig(td)
        fs = flt(td)
        if sign != 0:
            el = s[s == sign]
        else:
            el = s[abs(s) == 1]

        ret = {}
        fret = {}
        wd = 1200
        for i in el.index:
            eloc = td.index.get_loc(i)
            e = (td.close.iloc[-wd + eloc:wd + eloc] /
                 td.close.iloc[eloc] - 1) * el[i]
            e.index = range(-wd, len(e) - wd)
            ret[i] = e
            if is_absolute:
                fret[i] = fs.loc[i]
            else:
                fret[i] = fs.loc[i] * el[i]
        ret = pd.DataFrame(ret)
        fret = pd.Series(fret)
        return ret, fret

    def find_event(self, d, sig, flt, sign, is_absolute=False):
        ret, fret = self.find_ticker_event(d, sig, flt, sign, is_absolute)
        return ret, fret

    def plot_event(self, d, sig):
        td = d.data
        s = sig(td)
        s1 = td.close.reset_index(drop=True)
        s2 = td.close.where((s == 1), np.nan).reset_index(
            drop=True).plot(marker='^')
        s3 = td.close.where(
            (s == -1), np.nan).reset_index(drop=True).plot(marker='v')
        s1.plot(color='black', alpha=0.5)
        s2.plot(color='red')
        s3.plot(color='blue')
        return


def fetch(df):
    global settings

    factor_file = f"data/{settings['method']}_{settings['table']}.pkl"

    # if os.path.exists(factor_file):
    #     with open(factor_file, 'rb') as f:
    #         series = pickle.load(f)
    #     print(f"从本地加载因子数据->{factor_file}。")
    # else:
    #     response = requests.get(
    #         "http://************:6678",
    #         params={
    #             "method": settings["method"],
    #             "table": settings["table"]
    #         }
    #     )

    #     print(f"status: {response.status_code}")
    #     # print(f'error message:{response.json()}')

    #     json_data = response.json()
    #     series = pd.Series(json_data)
    #     series.index = pd.to_datetime(series.index.astype(float), unit='ms')

    #     # 将获取的数据保存到本地
    #     with open(factor_file, 'wb') as f:
    #         pickle.dump(series, f)
    #     print(f"从在线获取因子数据并保存本地->{factor_file}。")
    response = requests.get(
        "http://************:6678",
        params={
            "method": settings["method"],
            "table": settings["table"]
        }
    )

    print(f"status: {response.status_code}")
    # print(f'error message:{response.json()}')

    json_data = response.json()
    series = pd.Series(json_data)
    series.index = pd.to_datetime(series.index.astype(float), unit='ms')

    signals = series.iloc[settings["from_data"]:None if settings["to_data"]
                          == 0 else settings["to_data"]].reset_index(drop=True)

    f = F_price_position(df)

    f_upper = f.expanding().quantile(0.6)  # 橙线
    f_lower = f.expanding().quantile(0.2)  # 蓝线

    # signals[f < f_upper] = 0
    # signals[f > f_lower] = 0

    print('因子信号点数量:', signals.ne(0).sum())

    return signals


def F_historical_volatility(d):
    '''衡量当前波动率高低的过滤器'''
    df = d.copy()
    df['HV'] = np.log(
        (df['close']/df['close'].shift(1))).rolling(20).std()
    return df['HV']


def F_volume_deviation(d):
    '''衡量当前成交量高低的过滤器'''
    df = d.copy()
    df['volume_deviation'] = (
        df['volume']-df['volume'].rolling(20).mean())/df['volume'].rolling(20).mean()
    return df['volume_deviation']


def F_price_position(d):
    '''衡量当前相对位置高低的过滤器'''
    df = d.copy()
    df['up'] = df['high'].rolling(20).max()
    df['down'] = df['low'].rolling(20).min()
    df['price_position'] = (df['close']-df['down'])/(df['up']-df['down'])
    return df['price_position']


def F_price_fluctuation(d):
    '''衡量短期价格波动快慢的过滤器'''
    df = d.copy()
    df['price_fluctuation'] = df['close'].rolling(
        5).std()/df['close'].rolling(30).std()
    return df['price_fluctuation']


def filter_hub(df):
    s = F_price_position(df)
    # s = filter_2(df)

    s_min = s.min()
    s_max = s.max()
    print(f"s 的范围是 [{s_min}, {s_max}]")

    return s


def decay_signal(s: pd.Series, beta: float) -> pd.Series:
    return s + beta * s.shift(1)


settings = {

    # 你要回测的数据
    "table": "BTCUSDT_15m_2022_2025",
    # 你要调用的因子方法
    "method": "ret_ma_short_long_cross_sig_price",
    # 切片，from
    "from_data": -9999999,
    # 切片，to，如果填0就切到最后一个
    "to_data": 0,
    # 是否显示过滤图
    "show_filter": True,
    # "show_filter": False,
    # 是否显示k线图
    # "show_k_line": True,
    "show_k_line": False,
}


if __name__ == '__main__':
    d = Data(settings["table"])
    dm = DataManager()
    dm.concat_data(d)

    exit()

    params = ''

    filter_name = 'F_price_position'
    # path = f'C:/Users/<USER>/Desktop/40因子/{settings['method']}/filter/{settings['table']}_{filter_name}'
    print("K线数据数据总条数:", len(d.data))

    ea = EventAnal()
    td = ea.get_data(d)

    s = fetch(td)
    # print(s)
    # exit()

    el, ft = ea.find_ticker_event(d, fetch, filter_hub, 0, True)
    qc, bins = pd.qcut(ft.fillna(0), [0, 0.2, 0.4, 0.6, 0.8, 1],
                       labels=False, retbins=True, duplicates='drop')
    print("分割点：", bins)
    rs = el.T.groupby(qc).mean().T
    rs_mean = el.T.mean().T

    num = s.ne(0).sum()
    ##########################################################################################################################
    # # 因子评估
    # df = d.data.copy()
    # df['return'] = df['close'].shift(-150)/df['close']-1
    # df['return'] = df['return'].shift(-1)
    # df.fillna(0, inplace=True)
    # df = df.reset_index(drop=True)
    # print(f'因子:{settings["method"]}')
    # print(f'原始相关系数:{df['return'].corr(s)}')
    # ####################################################
    # # 填充1和-1的窗口内的值
    # s_modified = pd.Series(0, index=s.index)  # 初始化全0
    # idx_1 = s[s == 1].index
    # idx_neg1 = s[s == -1].index
    # window = 55
    # s_pos = pd.Series(0, index=s.index)
    # s_neg = pd.Series(0, index=s.index)
    # for i in idx_1:
    #     s_pos.iloc[i:i + window] = 1
    # for i in idx_neg1:
    #     s_neg.iloc[i:i + window] = -1

    # s_modified = s_pos + s_neg
    # print(f'处理后相关系数:{df['return'].corr(s_modified)}')
    ####################################################
    # 因子实体化
    # path2 = f'filled_factors/{settings['method']}_chu3.pkl'
    # with open(path2, 'wb') as f:
    #     pickle.dump(s_modified, f)
    # print('实体化完成!')
    # exit()
    ####################################################
    # 衰减函数

    ####################################################

    ##########################################################################################################################

    # ========================== 统计部分 ===============================
    # 计算经过的总时间（以毫秒计）
    total_duration_ms = td.close_time[len(td) - 1] - td.close_time[0]
    # 将毫秒转换为秒
    total_duration_seconds = total_duration_ms / 1000
    # 使用 divmod 计算天、小时和分钟
    days, remainder = divmod(total_duration_seconds,
                             86400)  # 86400s in one day
    hours, remainder = divmod(remainder, 3600)  # 3600s in one hour
    minutes = remainder / 60
    # 使用条件判断统计 1 的个数
    count_1 = np.sum(s == 1)
    count_minus_1 = np.sum(s == -1)
    text = f'经历了{int(days)}天{int(hours)}小时{int(minutes)}分钟，共开单：{count_1+count_minus_1}，多单: {count_1}，空单：{count_minus_1}。'
    # ========================== 统计部分 ===============================

    # 把rs的图和el.mean(axis=1).plot(color='black')的图显示在plotly上
    if settings["show_filter"]:
        fig = go.Figure()
        for i in range(len(bins) - 1):
            fig.add_trace(go.Scatter(
                x=rs.index, y=rs[i], mode='lines', name=f'{i}'))
        fig.add_trace(go.Scatter(x=rs_mean.index, y=rs_mean,
                      mode='lines', name='mean', line=dict(color='black')))
        # fig.add_trace(go.Scatter(x=el.mean(axis=1).index, y=el.mean(axis=1), mode='lines', name='mean', line=dict(color='black')))
        fig.update_layout(
            #####################################################################################################
            ### 修改过###
            title=f'币种:{settings['table']}\t\t因子:{settings["method"]}\t\t过滤器:{filter_name}\t\t信号点数量:{num}\t\t过滤器参数:{params}',
            #####################################################################################################
            annotations=[
                dict(
                    text=text + f'{bins}',

                    xref="paper", yref="paper",
                    x=0.5, y=1.15,  # 位置在标题上方
                    showarrow=False,
                    font=dict(size=12)
                )
            ],
            xaxis_title='Time',
            yaxis_title='Price',
            dragmode='zoom',
            xaxis=dict(rangeslider=dict(visible=True)),
            yaxis=dict(autorange=True),  # 添加这一行
            hovermode='x unified')
        fig.show()
        # pio.write_html(fig, f"{path}.html")
        print('done')

    if settings["show_k_line"]:
        fig = make_subplots(rows=2, cols=1,
                            shared_xaxes=True,
                            vertical_spacing=0.05,
                            row_heights=[0.7, 0.3])
        td['fs'] = filter_hub(td)

        s1 = td.close.reset_index(drop=True)
        s2 = td.close.where((s == 1), np.nan).reset_index(drop=True)
        s3 = td.close.where((s == -1), np.nan).reset_index(drop=True)

        # 添加行情线图到主图（row=1）
        fig.add_trace(go.Scatter(
            x=td.index,
            y=td['close'],
            name='价格',
            mode='lines',
            line={'simplify': True},  # 启用简化算法
        ), row=1, col=1)

        # 1. 买入信号
        buy_mask = ~np.isnan(s2)
        fig.add_trace(go.Scattergl(
            x=s1.index[buy_mask],
            y=s2[buy_mask],
            mode='markers',
            name='buy',
            marker=dict(
                color='purple',
                symbol='triangle-up',
                size=10
            )
        ), row=1, col=1)

        # 2. 卖出信号
        sell_mask = ~np.isnan(s3)
        fig.add_trace(go.Scattergl(
            x=s1.index[sell_mask],
            y=s3[sell_mask],
            mode='markers',
            name='sell',
            marker=dict(
                color='green',
                symbol='triangle-down',
                size=10
            )
        ), row=1, col=1)

        # 添加过滤器
        fig.add_trace(go.Scatter(
            x=td.index, y=td['fs'], name='过滤器'), row=2, col=1)

        # 统一布局设置
        fig.update_layout(
            title='交易分析',
            annotations=[
                dict(
                    text=text,
                    xref="paper", yref="paper",
                    x=0.5, y=1.15,  # 位置在标题上方
                    showarrow=False,
                    font=dict(size=12)
                )
            ],
            xaxis_title='时间',
            dragmode='zoom',
            hovermode='x unified',
            xaxis=dict(),
            yaxis=dict(
                title='价格',
                autorange=True,
                fixedrange=False,  # 允许Y轴拖动
                showspikes=True  # 显示悬停时的辅助线
            ),
            height=800  # 增加总图高度
        )
        fig.show()
        print('done')
