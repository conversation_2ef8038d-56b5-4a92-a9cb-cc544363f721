{"cells": [{"cell_type": "code", "execution_count": 1, "id": "61609677", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import requests"]}, {"cell_type": "code", "execution_count": 11, "id": "67506f32", "metadata": {}, "outputs": [], "source": ["url='https://api.binance.com/api/v1/depth'"]}, {"cell_type": "code", "execution_count": 12, "id": "bd581a29", "metadata": {}, "outputs": [], "source": ["params={\n", "    'symbol':'BTCUSDT',\n", "    'limit':10\n", "}"]}, {"cell_type": "code", "execution_count": 20, "id": "53065bff", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'lastUpdateId': 72415162693,\n", " 'bids': [['107773.47000000', '20.92514000'],\n", "  ['107773.46000000', '0.02245000'],\n", "  ['107773.26000000', '0.00627000'],\n", "  ['107772.90000000', '0.00010000'],\n", "  ['107772.76000000', '0.00005000'],\n", "  ['107772.75000000', '0.09294000'],\n", "  ['107772.74000000', '0.00139000'],\n", "  ['107772.53000000', '0.00005000'],\n", "  ['107772.52000000', '0.08521000'],\n", "  ['107772.23000000', '0.01833000']],\n", " 'asks': [['107773.48000000', '1.95603000'],\n", "  ['107773.49000000', '0.00055000'],\n", "  ['107773.69000000', '0.00058000'],\n", "  ['107774.00000000', '0.00100000'],\n", "  ['107774.03000000', '0.00005000'],\n", "  ['107774.04000000', '0.00005000'],\n", "  ['107774.18000000', '0.00006000'],\n", "  ['107774.21000000', '0.00149000'],\n", "  ['107774.61000000', '0.00005000'],\n", "  ['107774.62000000', '0.02019000']]}"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["resp=requests.get(url=url,params=params)\n", "resp.json()"]}, {"cell_type": "code", "execution_count": null, "id": "e23f5a54", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'lastUpdateId': 72415157392,\n", " 'bids': [['107773.47000000', '22.09151000'],\n", "  ['107773.46000000', '0.02245000'],\n", "  ['107773.26000000', '0.00627000'],\n", "  ['107773.25000000', '0.00045000'],\n", "  ['107772.76000000', '0.00005000'],\n", "  ['107772.75000000', '0.09304000'],\n", "  ['107772.74000000', '0.00139000'],\n", "  ['107772.53000000', '0.00005000'],\n", "  ['107772.52000000', '0.08526000'],\n", "  ['107772.50000000', '0.00010000']],\n", " 'asks': [['107773.48000000', '0.39584000'],\n", "  ['107773.49000000', '0.00045000'],\n", "  ['107773.69000000', '0.00058000'],\n", "  ['107774.00000000', '0.00100000'],\n", "  ['107774.03000000', '0.00005000'],\n", "  ['107774.04000000', '0.00005000'],\n", "  ['107774.18000000', '0.00006000'],\n", "  ['107774.21000000', '0.00149000'],\n", "  ['107774.61000000', '0.00005000'],\n", "  ['107774.62000000', '0.02019000']]}"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5f27d1e5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}