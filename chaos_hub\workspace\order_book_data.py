import websocket
import json
import os
import pickle
from datetime import datetime

# 设置代理（如果有）
os.environ["ALL_PROXY"] = "socks5://127.0.0.1:7897"
os.environ["HTTPS_PROXY"] = "socks5://127.0.0.1:7897"

# 存储所有数据的列表
all_data = []
SAVE_INTERVAL = 10000  # 每10000条保存一次
total_count = 0  # 总数据计数

# 数据保存路径
DATA_DIR = "C:/Users/<USER>/python sc/factor_evaluation/market_data"
os.makedirs(DATA_DIR, exist_ok=True)

def save_data_chunk():
    """保存当前数据块"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{DATA_DIR}/ws_data_{timestamp}_{len(all_data)}条.pkl"
    
    with open(filename, "wb") as f:
        pickle.dump(all_data, f)
    print(f"✅ 数据已保存到 {filename}（共 {len(all_data)} 条）")
    all_data.clear()  # 清空当前数据块

def on_message(ws, message):
    global total_count
    try:
        data = json.loads(message)
        all_data.append(data)  # 添加到全局列表
        total_count += 1
        
        # 实时显示数据计数（每100条打印一次）
        if total_count % 100 == 0:
            print(f"📊 已接收 {total_count} 条数据 | 当前块 {len(all_data)} 条")
        
        # 每10000条保存一次
        if len(all_data) >= SAVE_INTERVAL:
            save_data_chunk()
            
        # 如果是K线收盘事件，额外打印
        if 'k' in data and data['k']['x']:
            print(f"📈 K线收盘: {data['k']}")
            
    except Exception as e:
        print("⚠️ 数据处理错误:", e)

def on_error(ws, error):
    print("⚠️ WebSocket 错误:", error)

def on_close(ws, close_status_code, close_msg):
    print("⏹ WebSocket 连接关闭")
    if all_data:
        save_data_chunk()  # 连接关闭时保存剩余数据
    print(f"🔚 总计接收 {total_count} 条数据")

def on_open(ws):
    print("🟢 WebSocket 连接已建立")
    ws.send(json.dumps({
        "method": "SUBSCRIBE",
        "params": ["ethusdt@depth20"],  # 订阅 ETH/USDT 20档深度
        "id": 1
    }))

if __name__ == "__main__":
    # 启动 WebSocket
    ws = websocket.WebSocketApp(
        "wss://fstream.binance.com/ws",
        on_open=on_open,
        on_message=on_message,
        on_error=on_error,
        on_close=on_close
    )
    
    try:
        print("🚀 启动 WebSocket 数据采集...")
        print(f"💾 数据将每 {SAVE_INTERVAL} 条自动保存一次")
        ws.run_forever()
    except KeyboardInterrupt:
        print("\n🛑 手动终止程序")
        if all_data:
            save_data_chunk()  # Ctrl+C 时保存剩余数据
        print(f"🔚 总计接收 {total_count} 条数据")