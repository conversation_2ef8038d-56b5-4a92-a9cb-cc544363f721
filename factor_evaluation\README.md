# 因子评估系统

一个专业的量化因子分析和评估Python工具包，支持单币种和多币种因子评估，提供完整的因子有效性分析流程。

## 🚀 核心功能

- **📊 全面的因子评估**: 平稳性检验、相关性分析、信息比率、分组分析、分布分析
- **🔄 多币种支持**: 支持批量评估多个交易品种的因子表现
- **⚙️ 灵活的因子定义**: 支持自定义因子函数或直接传入因子数据
- **📈 结果可视化**: 自动生成分组收益图和因子分布图
- **💾 结果保存**: 自动保存评估结果为JSON格式，图表保存为PDF
- **🔧 统一流程**: 单币种和多币种评估采用相同的流程和显示格式
- **📋 结果加载**: 支持加载和查看历史评估结果

## 📦 环境要求

### Python版本
- Python 3.8+

### 依赖包
```bash
pip install pandas numpy matplotlib statsmodels sqlalchemy requests scipy seaborn
```

## 🎯 快速开始

### 基本用法

```python
from factor_evaluation import FactorEvaluation
import pandas as pd
import numpy as np

# 创建示例数据
dates = pd.date_range('2023-01-01', periods=1000, freq='H')
df = pd.DataFrame({
    'close': 100 + np.cumsum(np.random.randn(1000) * 0.01),
    'volume': np.random.randint(1000, 10000, 1000)
}, index=dates)

# 定义因子函数
def momentum_factor(df, period=20):
    return df['close'].pct_change(period) * 100

# 创建评估器并运行评估
evaluator = FactorEvaluation(
    df=df,
    factor_func=momentum_factor,
    factor_name='momentum_20',
    period=20
)

# 运行完整评估
results = evaluator.run_full_evaluation()
```

### 常用因子函数示例

```python
def momentum_factor(df, period=20):
    """动量因子：价格变化率"""
    return df['close'].pct_change(period) * 100

def ma_cross_factor(df, short_period=5, long_period=20):
    """移动平均交叉因子"""
    ma_short = df['close'].rolling(short_period).mean()
    ma_long = df['close'].rolling(long_period).mean()
    return (ma_short / ma_long - 1) * 100

def volatility_factor(df, period=20):
    """波动率因子"""
    returns = df['close'].pct_change()
    return returns.rolling(period).std() * 100

def rsi_factor(df, period=14):
    """RSI因子"""
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))
```

### 逐步分析

```python
# 单独运行各项分析
stationarity = evaluator.stationarity_test(alpha=0.05)
correlation = evaluator.correlation_analysis()
ir = evaluator.information_ratio(n_groups=10)
group_stats, fig = evaluator.group_analysis(n_groups=20, show_plot=True)
distribution = evaluator.factor_distribution_analysis(time_windows=10, show_hist=True)

# 查看结果
print("平稳性检验:", stationarity['is_stationary'])
print("IC值:", correlation['IC'])
print("Rank IC:", correlation['Rank_IC'])
print("信息比率:", ir['IR'])
```

### 多币种评估

```python
# 多币种评估（需要配置数据源）
evaluator = FactorEvaluation(
    time_periods=['1h'],  # 时间周期
    factor_func=momentum_factor,
    factor_name='momentum_multi',
    period=20
)

results = evaluator.run_full_evaluation()
```


## 📊 评估指标详解

### 1. 平稳性检验 (ADF检验)
- **目的**: 检验因子序列是否平稳
- **指标**: p值、是否平稳
- **意义**: 平稳的因子序列更适合用于预测
- **判断标准**: p值 < 0.05 认为序列平稳

### 2. 相关性分析
- **IC (Information Coefficient)**: Pearson相关系数，衡量线性相关性
- **Rank_IC**: Spearman相关系数，衡量单调相关性
- **意义**: 衡量因子与未来收益的相关程度
- **优秀标准**: |IC| > 0.05, |Rank_IC| > 0.05

### 3. 信息比率 (IR)
- **计算**: 分组相关系数的均值/标准差
- **意义**: 衡量因子的风险调整收益能力
- **优秀标准**: |IR| > 0.5

### 4. 分组分析
- **方法**: 将因子值分组，计算各组收益统计
- **输出**: 各组收益均值、标准差、样本数
- **可视化**: 分组收益柱状图
- **理想情况**: 分组收益呈单调性

### 5. 分布分析
- **描述统计**: 均值、标准差、分位数等
- **极端值分析**: 超过3σ、5σ的比例
- **时间窗口分析**: 不同时间段的分布特征
- **可视化**: 因子分布直方图

## 💾 结果保存与加载

### 自动保存
评估完成后，结果会自动保存到 `results/` 文件夹：
- **JSON文件**: 包含所有评估结果和因子函数信息
- **PDF文件**: 包含分组分析图和分布图

### 文件命名规则
- 单币种: `factor_evaluation_{因子名}_{时间戳}/`
- 多币种: `factor_evaluation_multi_{因子名}_{周期}_{时间戳}/`

### 结果加载
```python
# 加载最新结果
loader = evaluator.load_and_display_results()

# 加载指定结果
loader = evaluator.load_and_display_results('results/factor_evaluation_momentum_20231201_120000')

# 显示详细信息
loader.print_summary()           # 显示摘要
loader.print_detailed_results()  # 显示详细结果
loader.show_all_images()        # 显示所有图片
```

## 📋 数据要求

### 单币种模式
- DataFrame必须包含 `close` 列
- 建议包含时间索引（DatetimeIndex）
- 数据长度建议 > 100条
- 数据应无异常值和大量缺失值

### 多币种模式
- 需要配置DataService数据源
- 支持的币种：ETHUSDT, BTCUSDT, XRPUSDT, SOLUSDT
- 支持的时间周期：1h, 4h, 1d等
- 自动处理数据加载和预处理

## 🏗️ 项目结构

```
factor_evaluation/
├── factor_evaluation.py    # 🎯 核心模块：FactorEvaluation类和FactorResultLoader类
├── data_service.py         # 🔌 数据服务：多币种数据获取和API接口
├── group_stats.py          # 📊 分组分析：GroupStats类，因子分组统计
├── __init__.py             # 📦 包初始化文件
├── results/                # 💾 评估结果存储目录（自动创建）
│   ├── factor_evaluation_*/      # 单币种结果文件夹
│   └── factor_evaluation_multi_*/ # 多币种结果文件夹
└── README.md              # 📖 项目说明文档
```

### 📁 核心文件详解

#### 🎯 factor_evaluation.py
**主要功能模块，包含：**
- `FactorEvaluation` 类：核心评估引擎
- `FactorResultLoader` 类：结果加载和显示
- `analyze_factor_distribution` 函数：因子分布分析工具
- 完整的评估流程和结果保存功能

#### 🔌 data_service.py
**数据服务模块，提供：**
- `DataService` 类：统一的数据访问接口
- 支持MySQL数据库连接
- 支持API因子信号获取
- 双模式访问：K线数据和因子信号

#### 📊 group_stats.py
**分组统计分析模块：**
- `GroupStats` 类：因子分组分析工具
- 支持动态分组和极端值分析
- 自动生成分组收益可视化图表

### 📂 自动生成目录

- **results/**: 评估结果自动保存目录
  - 每次评估自动创建时间戳文件夹
  - 包含JSON结果文件和PDF图表文件
  - 支持单币种和多币种结果分类存储

## 🔧 核心API参考

### 🎯 FactorEvaluation 类

#### 初始化参数
```python
class FactorEvaluation:
    def __init__(self,
                 df=None,                    # 单币种模式：输入DataFrame
                 time_periods=None,          # 多币种模式：时间周期列表
                 future_return_periods=10,   # 未来收益计算周期
                 factor_col='signals',       # 因子列名
                 return_col='future_return', # 收益率列名
                 factor_func=None,           # 因子计算函数
                 factor_name=None,           # 因子名称
                 *factor_args,               # 因子函数位置参数
                 **factor_kwargs)            # 因子函数关键字参数
```

#### 主要方法
```python
# 🚀 完整评估流程
def run_full_evaluation(self,
                       n_groups_ir=10,        # IR计算分组数
                       n_groups_analysis=20,  # 分组分析分组数
                       time_windows=10,       # 时间窗口数
                       show_plots=True,       # 是否显示图表
                       print_results=True)    # 是否打印结果

# 📊 单项分析方法
def stationarity_test(self, alpha=0.05)                    # 平稳性检验
def correlation_analysis(self)                             # 相关性分析
def information_ratio(self, n_groups=10)                   # 信息比率
def group_analysis(self, n_groups=20, show_plot=True)      # 分组分析
def factor_distribution_analysis(self, time_windows=10, show_hist=True)  # 分布分析

# 💾 结果管理方法
def load_and_display_results(self, result_path=None, ...)  # 加载历史结果
def create_result_loader(self, result_path)                # 创建结果加载器
```

### 🔌 DataService 类
```python
class DataService:
    def __getitem__(self, key):
        # 双模式访问：
        # ds['ETHUSDT_1h_2020_2025'] → 返回K线数据
        # ds['ETHUSDT_1h_2020_2025', 'method_name'] → 返回因子信号
```

### 📊 GroupStats 类
```python
class GroupStats:
    def __init__(self, df, value_col, return_col)
    def analyze_groups(self, n_groups=10, show_plot=False)  # 分组分析
```

### 📋 FactorResultLoader 类
```python
class FactorResultLoader:
    def __init__(self, result_path)
    def print_summary(self)           # 显示结果摘要
    def print_detailed_results(self)  # 显示详细结果
    def show_all_images(self)         # 显示所有图片
    def export_results(self)          # 导出结果
```

## ⚠️ 使用注意事项

### 📋 数据要求
1. **数据质量**: 确保输入数据无异常值和大量缺失值
2. **数据格式**: DataFrame必须包含 `close` 列，建议包含时间索引
3. **数据长度**: 建议数据长度 > 100条，确保统计分析的有效性

### 🔧 因子设计
1. **返回类型**: 因子函数应返回pandas.Series或numpy.array
2. **数据对齐**: 确保因子数据与价格数据的时间索引对齐
3. **参数传递**: 支持通过*args和**kwargs传递因子函数参数

### ⚙️ 参数调优
1. **分组数量**: 根据数据量调整分组数量（建议10-50组）
2. **时间窗口**: 根据因子特性调整时间窗口分析参数
3. **未来收益周期**: 根据交易策略调整预测周期

### 🔌 多币种模式
1. **数据源配置**: 需要配置MySQL数据库连接
2. **网络连接**: 确保API服务可访问
3. **数据表命名**: 遵循 `{symbol}_{period}_{timerange}` 格式

### 💾 结果管理
1. **存储空间**: 注意results文件夹的磁盘空间使用
2. **文件清理**: 定期清理不需要的历史结果文件
3. **备份重要结果**: 对重要的评估结果进行备份

## 🔄 版本历史

### v2.1.0 (当前版本)
- ✅ 重构项目结构，简化文件组织
- ✅ 统一单币种和多币种评估流程
- ✅ 优化结果显示格式，先显示结果再显示图片
- ✅ 改进图片显示方式，使用plt.show()确保正确显示
- ✅ 清理未使用的代码、变量和导入
- ✅ 完善API文档和使用说明
- ✅ 集成所有功能到核心模块

### v2.0.0
- 统一评估流程
- 添加结果加载功能
- 支持多币种批量评估

### v1.0.0
- 初始版本发布
- 基础因子评估功能
- 结果保存和可视化

## 🚀 快速开始指南

### 1️⃣ 安装依赖
```bash
pip install pandas numpy matplotlib statsmodels sqlalchemy requests scipy seaborn
```

### 2️⃣ 导入模块
```python
from factor_evaluation import FactorEvaluation
```

### 3️⃣ 准备数据和因子
```python
import pandas as pd
import numpy as np

# 创建示例数据
dates = pd.date_range('2023-01-01', periods=1000, freq='H')
df = pd.DataFrame({
    'close': 100 + np.cumsum(np.random.randn(1000) * 0.01)
}, index=dates)

# 定义因子函数
def momentum_factor(df, period=20):
    return df['close'].pct_change(period) * 100
```

### 4️⃣ 运行评估
```python
# 创建评估器
evaluator = FactorEvaluation(
    df=df,
    factor_func=momentum_factor,
    factor_name='momentum_20',
    period=20
)

# 运行完整评估
results = evaluator.run_full_evaluation()
```

### 5️⃣ 查看结果
```python
# 加载历史结果
loader = evaluator.load_and_display_results()
```

## 📞 技术支持

- 📖 **文档**: 查看代码注释和本README文档
- 🔍 **调试**: 根据错误信息和日志进行问题排查
- 💡 **示例**: 参考factor_evaluation.py中的__main__部分示例代码

## 📄 许可证

本项目采用MIT许可证开源。
