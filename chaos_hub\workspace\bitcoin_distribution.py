import requests
from lxml import etree
import csv

cookies = {
    '_ga': 'GA1.1.2051276271.1742287399',
    '_cc_id': 'c5d5c45e11bd85f81613e7dad134af74',
    '_xicah': '4d985af1-10b44c36',
    'panoramaId_expiry': '1743648900611',
    'panoramaId': 'ca24bded4e3f0d5873adcdf496ed185ca02ca84a3078cf1f82a00e4b868ced6d',
    'panoramaIdType': 'panoDevice',
    'cf_clearance': 'HEgZUUNPZtA_Q6u5zbpygFDUtcvGq6h.JibW2.Gh0mE-1743045703-*******-fOGttzdJcWSvSo0udaWU5SWNWBCVJjsh5x6bZVvm7Z2pM0VSzZw.rNVKM_hjjsSOGUZGbnuEnPre.6TJewCvr_THz3Fd9c2Xg.pECQfXji0rc0XPO7pXVTYDGrlG7qK2fgyvA6Hqtav2UJoNUn6rvdNm.pJ8a8vdNNBUGGmrODeAWvL.qzF8F4yZHh3o4SbbZUwvpQdZspr7L3hiymlse_beRI55r1mxXZmbTk_uVUZ4mg9qd789Nl19K4_tKEbN6_gw5Tm3rdiqmJcaIEqEpm9ecSqg2mU2cG4UCZbVpA4SXNkEIhtT33wAvoZFdZ.Xh6w9aAdwFdryAT4a1cI3ZYbxSy7FhWgFnHpnPMXmLWH.7MuWThaXdO_XasyrM7YChIPoUWi0nKU5hCiwQH0ufrHBnkTllNQb6S.r.cWctjY',
    '__gads': 'ID=79772c137d2db55d:T=1742287407:RT=1743046512:S=ALNI_MZrAKhFbqXGaYZvEjwAkmGDNoAlqA',
    '__gpi': 'UID=0000106659f59c2e:T=1742287407:RT=1743046512:S=ALNI_MZQokM22LniUjHBFD-6Zwvzk1Ku9w',
    '__eoi': 'ID=a2de7bec6af41a5a:T=1742287407:RT=1743046512:S=AA-AfjaBV0oYryf-v4Uu1vvpJduw',
    '_ga_W36YPG68C0': 'GS1.1.1743044090.2.1.1743046682.0.0.0',
    'FCNEC': '%5B%5B%22AKsRol_BEfyVUh3iFmOKMIzWb4OUUbaSdT7BJHvNMmpfYGoAOLeyta9zuVjyLlNHPeJYNsaw9lJ1uXY2wqqfMMcWX-3qwdcNiBnA0j85dwsOBYFCAFx0DGx6Hpa6k1411EUsImiLlUXkjwWkqRQdC1ALRSPsQG-LEQ%3D%3D%22%5D%5D',
}

headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
    'cache-control': 'max-age=0',
    'if-modified-since': 'Thu, 27 Mar 2025 03:28:32 GMT',
    'priority': 'u=0, i',
    'referer': 'https://bitinfocharts.com/zh/top-100-richest-bitcoin-addresses.html',
    'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Microsoft Edge";v="134"',
    'sec-ch-ua-arch': '"x86"',
    'sec-ch-ua-bitness': '"64"',
    'sec-ch-ua-full-version': '"134.0.3124.83"',
    'sec-ch-ua-full-version-list': '"Chromium";v="134.0.6998.118", "Not:A-Brand";v="********", "Microsoft Edge";v="134.0.3124.83"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-model': '""',
    'sec-ch-ua-platform': '"Windows"',
    'sec-ch-ua-platform-version': '"19.0.0"',
    'sec-fetch-dest': 'document',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-user': '?1',
    'upgrade-insecure-requests': '1',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    # 'cookie': '_ga=GA1.1.2051276271.1742287399; _cc_id=c5d5c45e11bd85f81613e7dad134af74; _xicah=4d985af1-10b44c36; panoramaId_expiry=1743648900611; panoramaId=ca24bded4e3f0d5873adcdf496ed185ca02ca84a3078cf1f82a00e4b868ced6d; panoramaIdType=panoDevice; cf_clearance=HEgZUUNPZtA_Q6u5zbpygFDUtcvGq6h.JibW2.Gh0mE-1743045703-*******-fOGttzdJcWSvSo0udaWU5SWNWBCVJjsh5x6bZVvm7Z2pM0VSzZw.rNVKM_hjjsSOGUZGbnuEnPre.6TJewCvr_THz3Fd9c2Xg.pECQfXji0rc0XPO7pXVTYDGrlG7qK2fgyvA6Hqtav2UJoNUn6rvdNm.pJ8a8vdNNBUGGmrODeAWvL.qzF8F4yZHh3o4SbbZUwvpQdZspr7L3hiymlse_beRI55r1mxXZmbTk_uVUZ4mg9qd789Nl19K4_tKEbN6_gw5Tm3rdiqmJcaIEqEpm9ecSqg2mU2cG4UCZbVpA4SXNkEIhtT33wAvoZFdZ.Xh6w9aAdwFdryAT4a1cI3ZYbxSy7FhWgFnHpnPMXmLWH.7MuWThaXdO_XasyrM7YChIPoUWi0nKU5hCiwQH0ufrHBnkTllNQb6S.r.cWctjY; __gads=ID=79772c137d2db55d:T=1742287407:RT=1743046512:S=ALNI_MZrAKhFbqXGaYZvEjwAkmGDNoAlqA; __gpi=UID=0000106659f59c2e:T=1742287407:RT=1743046512:S=ALNI_MZQokM22LniUjHBFD-6Zwvzk1Ku9w; __eoi=ID=a2de7bec6af41a5a:T=1742287407:RT=1743046512:S=AA-AfjaBV0oYryf-v4Uu1vvpJduw; _ga_W36YPG68C0=GS1.1.1743044090.2.1.1743046682.0.0.0; FCNEC=%5B%5B%22AKsRol_BEfyVUh3iFmOKMIzWb4OUUbaSdT7BJHvNMmpfYGoAOLeyta9zuVjyLlNHPeJYNsaw9lJ1uXY2wqqfMMcWX-3qwdcNiBnA0j85dwsOBYFCAFx0DGx6Hpa6k1411EUsImiLlUXkjwWkqRQdC1ALRSPsQG-LEQ%3D%3D%22%5D%5D',
}

response = requests.get(
    'https://bitinfocharts.com/zh/bitcoin/address/**********************************-nodusting',
    cookies=cookies,
    headers=headers,
)

print(response)

html_content = response.text  # 网页源代码

# 解析 HTML
parser = etree.HTMLParser()
tree = etree.fromstring(html_content, parser)

# 使用 XPath 提取 class="table abtb tablesorter tablesorter-default" 的表格
tables = tree.xpath('//*[@id="table_maina"]')

# 目标 CSV 文件路径
csv_filename = r"C:\Users\<USER>\python sc\workspace\btc_distribution2.csv"

# 遍历表格，提取数据并保存到 CSV
with open(csv_filename, 'w', newline='', encoding='utf-8') as f:
    writer = csv.writer(f)
    
    for i, table in enumerate(tables):
        print(f"\n处理表格 {i+1}...")

        # 获取表头（thead）
        thead = table.xpath('.//thead[1]')
        if thead:
            header_row = [''.join(cell.xpath('.//text()')).strip() for cell in thead[0].xpath('.//th')]
            writer.writerow(header_row)  # 写入表头

        # 获取第二个 tbody
        tbody = table.xpath('.//tbody[2]')
        if tbody:
            rows = tbody[0].xpath('.//tr')  # 获取所有行
            for row in rows:
                cells = [''.join(cell.xpath('.//text()')).strip() for cell in row.xpath('.//td')]
                writer.writerow(cells)  # 写入数据
    
    print(f"表格数据已保存到 {csv_filename}")