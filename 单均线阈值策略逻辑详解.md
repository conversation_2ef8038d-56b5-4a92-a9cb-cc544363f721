# 单均线阈值策略逻辑详解

## 策略概述
单均线阈值策略（SingleMAThresholdStrategy）是一个基于单一长期移动平均线的趋势跟踪策略，具有复杂的多层平仓机制。

## 核心逻辑流程

### 1. 数据准备阶段
```
每根K线更新时：
├── 更新ArrayManager（K线数据管理器）
├── 检查数据是否充足（需要足够数据计算均线）
├── 更新所有持仓的K线计数器（bars_held += 1）
└── 计算技术指标
```

### 2. 技术指标计算
```
技术指标计算：
├── 长期移动平均线（MA）：使用long_window参数（默认20）
├── 价格范围：过去range_window个K线的（最高价-最低价）（默认200）
└── 信号阈值：均线 ± threshold（默认0）
```

### 3. 信号判断逻辑

#### 做多信号条件
```
做多信号 = t-2时刻收盘价 < t-2时刻长期均线
         AND
         t-1时刻收盘价 > t-1时刻长期均线+阈值
         AND
         t时刻收盘价 > t时刻长期均线+阈值
```

#### 做空信号条件
```
做空信号 = t-2时刻收盘价 > t-2时刻长期均线
         AND
         t-1时刻收盘价 < t-1时刻长期均线-阈值
         AND
         t时刻收盘价 < t时刻长期均线-阈值
```

### 4. 平仓逻辑（核心复杂部分）

#### 4.1 反向信号平仓（最高优先级）
```
IF 当前持有多单 AND 出现做空信号：
    ├── 平掉所有多单
    ├── 重置利润平仓计数器 = 0
    └── 立即开空单

IF 当前持有空单 AND 出现做多信号：
    ├── 平掉所有空单
    ├── 重置利润平仓计数器 = 0
    └── 立即开多单
```

#### 4.2 利润平仓逻辑

**多单利润平仓：**
```
价格范围 = 过去200个K线的（最高价-最低价）
利润差值 = 当前收盘价 - 当前均线

IF 利润平仓计数 == 0 AND 利润差值 >= 3 * 价格范围：
    ├── 平掉1500元金额的多单
    └── 利润平仓计数 = 1

IF 利润平仓计数 == 1 AND 利润差值 >= 5 * 价格范围：
    ├── 平掉2000元金额的多单
    └── 利润平仓计数 = 2
```

**空单利润平仓：**
```
利润差值 = 当前均线 - 当前收盘价

IF 利润平仓计数 == 0 AND 利润差值 >= 3 * 价格范围：
    ├── 平掉1500元金额的空单
    └── 利润平仓计数 = 1

IF 利润平仓计数 == 1 AND 利润差值 >= 5 * 价格范围：
    ├── 平掉2000元金额的空单
    └── 利润平仓计数 = 2
```

#### 4.3 时间平仓逻辑
```
IF 利润平仓计数 >= 2：
    FOR 每个持仓：
        IF 持仓K线数 >= 800：
            ├── 平掉1500元金额的仓位
            └── 跳出循环（一次只平一个仓位）
```

### 5. 开仓逻辑

#### 5.1 做多开仓
```
IF 做多信号触发：
    ├── 如果当前持有空仓 → 先平掉所有空仓
    ├── 计算开仓数量 = 5000元 / 当前价格
    ├── 检查可用资金是否充足
    ├── 执行买入操作
    ├── 创建新的Position对象
    └── 更新持仓状态
```

#### 5.2 做空开仓
```
IF 做空信号触发：
    ├── 如果当前持有多仓 → 先平掉所有多仓
    ├── 计算开仓数量 = 5000元 / 当前价格
    ├── 检查可用资金是否充足
    ├── 执行卖空操作
    ├── 创建新的Position对象
    └── 更新持仓状态
```

## 关键参数说明

### 主要参数
- `long_window = 20`：长期均线窗口
- `threshold = 0.0`：信号阈值
- `position_size = 5000`：每次开仓金额（元）
- `order_type = "amount"`：按金额下单

### 平仓参数
- `range_window = 200`：计算价格范围的窗口
- `profit_close_volume_1 = 1500`：第一次利润平仓金额
- `profit_close_volume_2 = 2000`：第二次利润平仓金额
- `profit_multiplier_1 = 3`：第一次利润平仓倍数
- `profit_multiplier_2 = 5`：第二次利润平仓倍数
- `time_close_bars = 800`：时间平仓K线数
- `time_close_volume = 1500`：时间平仓金额

## 策略状态管理

### 持仓状态
```
current_pos_direction：当前持仓方向
├── None：无持仓
├── "long"：持有多单
└── "short"：持有空单

positions：持仓列表
├── 每个Position包含：direction, price, volume, entry_time, bars_held
└── 支持多个持仓同时存在
```

### 平仓计数器
```
profit_close_count：利润平仓次数
├── 0：未进行利润平仓
├── 1：已进行第一次利润平仓
└── 2：已进行第二次利润平仓（可进行时间平仓）
```

## 资金管理

### 可用资金计算
```
可用资金 = 初始资金 - 已使用资金
已使用资金 = Σ(持仓价格 × 持仓数量)
```

### 风险控制
- 每次开仓前检查可用资金
- 支持按金额固定开仓，避免过度杠杆
- 多层平仓机制控制风险

## 执行顺序总结

```
每根K线处理顺序：
1. 更新数据和技术指标
2. 检查反向信号平仓（最高优先级）
3. 检查利润平仓条件
4. 检查时间平仓条件
5. 处理新的开仓信号
6. 更新历史数据用于下次判断
```

这个策略的核心特点是多层次的风险管理和利润保护机制，通过反向信号、利润目标和时间止损三重保护来管理持仓风险。
