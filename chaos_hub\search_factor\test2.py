from gplearn_factor_extraction import FactorMining
from sqlalchemy import create_engine
from math_operators import MathOperators
import pandas as pd
import numpy as np
from time import time
from data_service import DataService
from gplearn.functions import make_function


if __name__ == "__main__":
    # ds = DataService()
    # df = ds['ETHUSDT_15m_2020_2025']['2021-10-01':]

    # c_chu_list = []
    # for i in range(1, 53):  # 1 到 52
    #     item = f"c_chu{i:03d}"  # 格式化数字为 3 位，不足补 0
    #     c_chu_list.append(item)

    # for c in c_chu_list:
    #     df[c] = ds['ETHUSDT_15m_2020_2025', c]['2021-10-01':]
    #     print(c, '已完成')

    # # 把df存成pickle
    # df.to_pickle('df_data.pickle')
    # exit()

    # 初始化并运行因子挖掘
    df = pd.read_pickle('df_data.pickle')
    features = [x for x in df.columns.to_list() if 'chu' in x]
    # features = list(df.columns)
    target_column = "close"

    start_time = time()
    factor_miner = FactorMining(
        data=df,
        features=features,
        target_column=target_column,
        target_period=10,                          # 预测 target_period 期后的收益
        population_size=1000,                        # 种群规模
        generations=100,                             # 进化代数
        function_set=('add', 'sub', 'mul', 'div',
                      'sqrt', 'log', 'abs', 'neg', 'inv', 'sin', 'cos', 'tan'),        # 表达式
        test_size=0.2,
        random_state=42,
        metric='pearson'
    )

    # 获取多个表达式（前20个）
    results = factor_miner.run_multiple(top_n=20)
    end_time = time()

    print("\n========= 运行结果 =========")
    print(f"运行时间: {end_time - start_time:.2f} 秒")
    print(f"共找到 {len(results)} 个有效表达式")
    print("\n========= 前20个最佳因子表达式 =========")

    # 按 IC 值排序显示
    results_sorted_by_ic = sorted(
        results, key=lambda x: abs(x['ic']), reverse=True)

    print("\n--- 按 IC 绝对值排序 ---")
    for i, result in enumerate(results_sorted_by_ic):
        print(f"\n排名 {i+1}:")
        print(f"  表达式: {result['expression']}")
        print(f"  IC (Pearson): {result['ic']:.6f}")
        print(f"  Rank IC (Spearman): {result['rank_ic']:.6f}")
        print(f"  适应度: {result['fitness']:.6f}")
        print("-" * 80)
