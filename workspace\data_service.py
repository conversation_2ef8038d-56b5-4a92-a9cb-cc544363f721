import pandas as pd
import requests
from sqlalchemy import create_engine, text
from typing import Union, Tuple


class DataService:
    _MYSQL_CONFIG = {
        "host": "**************:33306",
        "user": "root",
        "password": "1234",
        "database": "ctadata"
    }
    _API_ENDPOINT = "http://**************:56678"

    def __init__(self):
        self.engine = create_engine(
            f"mysql+pymysql://{self._MYSQL_CONFIG['user']}:{self._MYSQL_CONFIG['password']}"
            f"@{self._MYSQL_CONFIG['host']}/{self._MYSQL_CONFIG['database']}"
        )

    def __getitem__(self, key: Union[str, Tuple[str, str]]) -> Union[pd.DataFrame, pd.Series]:
        """
        双模式访问：
        - ds['表名']          → 返回K线数据
        - ds['表名', '方法名'] → 返回因子信号
        """
        if isinstance(key, str):
            return self._fetch_kline_data(key)
        elif isinstance(key, tuple) and len(key) == 2:
            table, method = key
            return self._fetch_factor_signal(table, method)
        else:
            raise KeyError("无效键格式，应为字符串或 (表名, 方法名) 元组")

    def _fetch_kline_data(self, table: str) -> pd.DataFrame:
        """获取K线数据"""
        with self.engine.connect() as connection:
            df = pd.read_sql_query(text(f"SELECT * FROM {table}"), connection)
        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
        return df.set_index('open_time').sort_index()

    def _fetch_factor_signal(self, table: str, method: str) -> pd.Series:
        """获取因子信号"""
        response = requests.get(
            self._API_ENDPOINT,
            params={"method": method, "table": table}
        )
        response.raise_for_status()

        json_data = response.json()
        series = pd.Series(json_data)
        series.index = pd.to_datetime(series.index.astype(float), unit='ms')
        return series.sort_index().rename(f'{method}_signal')
