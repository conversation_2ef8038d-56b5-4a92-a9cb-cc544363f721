#!/bin/bash

# save_json.sh - 以backtest_writer用户身份保存JSON文件
# 用法: bash save_json.sh <源文件路径> <目标文件路径>

# 检查参数数量
if [ $# -ne 2 ]; then
    echo "错误: 参数数量不正确"
    echo "用法: $0 <源文件路径> <目标文件路径>"
    exit 1
fi

SOURCE_FILE="$1"
TARGET_FILE="$2"

# 检查源文件是否存在
if [ ! -f "$SOURCE_FILE" ]; then
    echo "错误: 源文件不存在: $SOURCE_FILE"
    exit 1
fi

# 假设目标目录已存在（根目录/results结构）

# 显示当前执行用户信息
echo "🔧 Shell脚本调试信息:"
echo "   当前执行用户: $(whoami)"
echo "   用户ID: $(id -u)"
echo "   用户组: $(id -g)"
echo "   源文件: $SOURCE_FILE"
echo "   目标文件: $TARGET_FILE"

# 复制文件到目标位置
cp "$SOURCE_FILE" "$TARGET_FILE"

# 检查复制是否成功
if [ $? -eq 0 ]; then
    # 显示最终文件信息
    if [ -f "$TARGET_FILE" ]; then
        echo "   ✅ JSON文件复制成功"
        echo "   📁 最终文件所有者: $(stat -c '%U' "$TARGET_FILE" 2>/dev/null || echo 'unknown')"
        echo "   📁 文件权限: $(stat -c '%a' "$TARGET_FILE" 2>/dev/null || echo 'unknown')"
    fi
    exit 0
else
    echo "   ❌ JSON文件保存失败"
    exit 1
fi
