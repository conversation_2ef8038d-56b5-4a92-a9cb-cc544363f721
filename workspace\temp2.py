import pandas as pd
import numpy as np
import warnings
def dynamic_quantile_weighted_returns_vectorized(factor_df, ret_df, n_groups=5):
    """
    动态分位数分层，并计算每组内因子值归一化后的加权平均收益（向量化版本）。

    具体逻辑：
    1. 在每个时间点，根据因子值将资产分成 n_groups 组。
    2. 对于每个分组：
       a. 对该组内所有资产的因子值进行归一化，使得这些归一化后的因子值之和为 1。
       b. 使用这些归一化后的因子值作为权重，计算该组资产的加权平均收益。
    
    :param factor_df: 因子值DataFrame (T×N)，允许部分NaN。
                      T 代表时间点数量，N 代表资产数量。
    :param ret_df: 收益率DataFrame (T×N)，与factor_df同形状。
    :param n_groups: 分层数，默认为 5。
    :return: 分层加权平均收益DataFrame (T×n_groups)。
             列名将是 'Q1', 'Q2', ..., 'Qn_groups'。
    """
    if not factor_df.index.equals(ret_df.index) or not factor_df.columns.equals(ret_df.columns):
        raise ValueError("`factor_df` 和 `ret_df` 必须拥有相同的索引和列名。")

    # 1. 逐行（时间点）进行分位数分组
    # group_labels_df 用于存储每个资产在每个时间点被分到的组标签
    group_labels_df = pd.DataFrame(np.nan, index=factor_df.index, columns=factor_df.columns)

    for date in factor_df.index:
        curr_factor_series = factor_df.loc[date].dropna()
        if len(curr_factor_series) > n_groups:
            # pd.qcut 对当前时间点的非NaN因子值进行分组
            bins = pd.qcut(
                curr_factor_series,
                q=n_groups,
                labels=False, # 返回整数标签 (0 到 n_groups-1)
                duplicates='drop' # 处理因子值重复的情况
            )
            # 将分组标签放回原始的 group_labels_df 中，只填充非NaN的部分
            group_labels_df.loc[date, bins.index] = bins

    # 2. 准备数据进行组内归一化和加权平均
    # 将因子值、收益率和分组标签堆叠起来，创建长格式的DataFrame
    stacked_factor = factor_df.stack(dropna=True).rename('factor')
    stacked_ret = ret_df.stack(dropna=True).rename('returns')
    stacked_groups = group_labels_df.stack(dropna=True).rename('group')

    # 将所有堆叠的数据合并到一个大的 DataFrame 中
    # `axis=1` 会按列合并，结果的行索引是 MultiIndex (日期, 资产)
    combined_df = pd.concat([stacked_factor, stacked_ret, stacked_groups], axis=1)

    # 过滤掉那些没有被成功分到组的行（即 'group' 列为 NaN 的行）
    combined_df = combined_df.dropna(subset=['group'])
    combined_df['group'] = combined_df['group'].astype(int) # 确保分组是整数类型

    # 3. 组内归一化因子值
    # **关键修正点：** 使用 `by` 参数指定分组的键。
    # 第一个键是日期（通过 `combined_df.index.get_level_values(0)` 获取 MultiIndex 的第一个层级的值）
    # 第二个键是 `group` 列的值（`combined_df['group']`）
    group_sum_factors = combined_df.groupby(
        by=[combined_df.index.get_level_values(0), combined_df['group']]
    )['factor'].transform('sum')

    # 避免除以零，将因子和为 0 的地方设为 NaN，这样在下一步计算中就不会参与
    normalized_weights = combined_df['factor'] / group_sum_factors
    normalized_weights[group_sum_factors == 0] = np.nan

    # 4. 计算加权平均收益
    # 权重乘以收益
    weighted_product = normalized_weights * combined_df['returns']

    # 再次按 (日期, 组号) 分组，然后求和得到加权平均收益
    # **关键修正点：** 再次使用正确的 `groupby` 方式
    group_weighted_returns = weighted_product.groupby(
        by=[weighted_product.index.get_level_values(0), combined_df['group']] # 注意这里仍然用 combined_df['group']
    ).sum()

    # 5. 重塑结果为 T x n_groups DataFrame
    # `group_weighted_returns` 的索引现在是 MultiIndex (日期, 组号)
    # 使用 `unstack(level=-1)` 将最后一个层级（即组号）从索引转换为列
    final_returns_df = group_weighted_returns.unstack(level=-1)

    # 确保列名是 'Q1', 'Q2', ..., 'Qn' 的形式
    # unstack 后的列名是 0 到 n_groups-1 的整数
    final_returns_df.columns = [f'Q{int(col)+1}' for col in final_returns_df.columns]

    # 确保所有 n_groups 列都存在，缺失的填充 NaN
    all_q_cols = [f'Q{i+1}' for i in range(n_groups)]
    final_returns_df = final_returns_df.reindex(columns=all_q_cols)

    # 确保最终 DataFrame 的索引与原始 factor_df 相同，补齐可能缺失的日期
    # 在最终结果生成时同时补全行列
    all_dates = factor_df.index
    all_cols = [f'Q{i+1}' for i in range(n_groups)]
    return final_returns_df.reindex(index=all_dates, columns=all_cols)