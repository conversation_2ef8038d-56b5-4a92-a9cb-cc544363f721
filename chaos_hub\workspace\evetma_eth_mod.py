
import datetime as dt
import os
import pandas as pd
import pickle
import numpy as np
from scipy.signal import find_peaks
import matplotlib.pyplot as plt
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from sqlalchemy import create_engine

MYSQL_CONFIG = {
    "host": "************",
    "user": "root",
    "password": "1234",
    "database": "ctadata"
}


class DataManager():

    def __init__(self):
        # self.__tim__ = ticker.TickerInfoManager()
        self.problem_ticker = []
        self.engine = create_engine(f"mysql+pymysql://{MYSQL_CONFIG['user']}:{
                                    MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}/{MYSQL_CONFIG['database']}")
        return

    def concat_data(self, data):
        """读取并合并单品类永续合约的K线数据"""
        query = f"SELECT * FROM {data.contract}"
        df = pd.read_sql(query, self.engine)
        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
        df.set_index('open_time', inplace=True)
        # 将所有K线数据合并
        data.data = df
        return


class Data():

    def __init__(self, contract):
        self.contract = contract
        return


class EventAnal():

    def __init__(self):
        return

    def get_data(self, d):
        """获取最新的5000条K线数据"""
        td = d.data
        return td.iloc[-5000:].reset_index(drop=True)

    def find_ticker_event(self, d, sig, flt, sign, is_absolute=False):
        """查找事件并计算收益"""
        td = d.data
        s = sig(td)
        fs = flt(td)
        if sign != 0:
            el = s[s == sign]
        else:
            el = s[abs(s) == 1]

        ret = {}
        fret = {}
        for i in el.index:
            eloc = td.index.get_loc(i)
            e = (td.close.iloc[-1201 + eloc:1200 + eloc] /
                 td.close.iloc[eloc] - 1) * el[i]
            e.index = range(-1200, len(e) - 1200)
            ret[i] = e
            if is_absolute:
                fret[i] = fs.loc[i]
            else:
                fret[i] = fs.loc[i] * el[i]
        ret = pd.DataFrame(ret)
        fret = pd.Series(fret)
        return ret, fret

    def find_event(self, d, sig, flt, sign, is_absolute=False):
        ret, fret = self.find_ticker_event(d, sig, flt, sign, is_absolute)
        return ret, fret

    def plot_event(self, d, sig):
        td = d.data
        s = sig(td)
        s1 = td.close.reset_index(drop=True)
        s2 = td.close.where((s == 1), np.nan).reset_index(
            drop=True).plot(marker='^')
        s3 = td.close.where(
            (s == -1), np.nan).reset_index(drop=True).plot(marker='v')
        s1.plot(color='black', alpha=0.5)
        s2.plot(color='red')
        s3.plot(color='blue')
        return


###########################################################################################
'''章鱼哥的自定义函数'''


def smma(series, length):
    """ 计算平滑移动平均 (SMMA) """
    smma_values = series.ewm(alpha=1/length, adjust=False).mean()
    return smma_values


def emma(series, length):
    """ 计算指数加权移动平均 (EMMA) """
    emma_values = series.ewm(span=length, adjust=False).mean()
    return emma_values


def rolling_low(series, p):
    """计算 t-2p 到 t-1 的最小值，确保不使用未来数据"""
    return series.shift(1).rolling(window=2*p, min_periods=1).min()


def rolling_high(series, p):
    """计算 t-2p 到 t-1 的最大值，确保不使用未来数据"""
    return series.shift(1).rolling(window=2*p, min_periods=1).max()


def rsi_deviation(df, rsi_col='rsi', price_col='close', p=3, signal_shift=None):
    """计算 RSI 背离因子(牛市背离 = 1,熊市背离 = -1,其余 = 0)"""
    df = df.copy()

    # 如果 signal_shift 为空，默认使用 t-(p+1) 作为信号点
    if signal_shift is None:
        signal_shift = p + 1

    # 计算局部高低点（基于 t-2p 到 t-1）
    df['RollingLow'] = rolling_low(df[price_col], p)
    df['RollingHigh'] = rolling_high(df[price_col], p)
    df['RSI_Low'] = rolling_low(df[rsi_col], p)
    df['RSI_High'] = rolling_high(df[rsi_col], p)

    # 牛市背离：价格创新低，RSI 没创新低
    price_condition_bullish = df[price_col].shift(
        signal_shift) <= df['RollingLow'].shift(signal_shift)
    rsi_condition_bullish = df[rsi_col].shift(
        signal_shift-1) >= df['RSI_Low'].shift(signal_shift)

    bullish_signal = np.where(price_condition_bullish &
                              rsi_condition_bullish, 1, 0)

    # 熊市背离：价格创新高，RSI 没创新高
    price_condition_bearish = df[price_col].shift(
        signal_shift) >= df['RollingHigh'].shift(signal_shift)
    rsi_condition_bearish = df['RSI_High'].shift(
        signal_shift) >= df[rsi_col].shift(signal_shift-1)

    bearish_signal = np.where(price_condition_bearish &
                              rsi_condition_bearish, -1, 0)

    # 计算 RSI 背离因子（牛市背离 = 1，熊市背离 = -1，其余 = 0）
    df['RSI_Divergence_Factor'] = bullish_signal + bearish_signal

    # 添加过滤条件
    df['RSI_Divergence_Factor'] = np.where(
        # (df['ma_ratio'] < 0.9996),
        (df['ma_ratio'] > 1.0095),
        # 1,
        df['RSI_Divergence_Factor'],
        0
    )

    print(df['RSI_Divergence_Factor'].value_counts())
    # exit()

    return df['RSI_Divergence_Factor']


def chao2_factor(df):
    # 根据 RSI 判断市场是否超买（高）或超卖（低）
    overbought = df['rsi'] > 70
    oversold = df['rsi'] < 30

    # 根据 ATR 判断市场波动性
    high_volatility = df['atr'] > df['atr'].rolling(
        window=20).mean()  # ATR大于20日均值为高波动

    # 根据 Volume ROC 判断交易量变化率
    high_volume_change = df['volume_roc'] > 0.1  # 假设Volume ROC大于0.1为显著变化

    # 初始化因子列
    df['chao2'] = 0

    # 超买且高波动、高交易量：可能意味着回调，赋负权重
    df.loc[overbought & high_volatility & high_volume_change, 'chao2'] = -1

    # 超卖且高波动、高交易量：可能意味着反转，赋正权重
    df.loc[oversold & high_volatility & high_volume_change, 'chao2'] = 1

    # 超买且低波动或交易量变化不大，市场趋于稳定，不赋权重
    df.loc[overbought & ~high_volatility | ~high_volume_change, 'chao2'] = 0

    # 超卖且低波动或交易量变化不大，市场趋于稳定，不赋权重
    df.loc[oversold & ~high_volatility | ~high_volume_change, 'chao2'] = 0

    return df['chao2']


def ret_ma_cross_sig_price(d):
    c = d.close
    long_T = 30
    short_T = 5

    ma_short = c.rolling(window=short_T).mean()
    ma_long = c.rolling(window=long_T).mean()

    high_short = c.rolling(window=short_T).max()
    high_long = c.rolling(window=long_T).max()
    low_short = c.rolling(window=short_T).min()
    low_long = c.rolling(window=long_T).min()

    vol_short = c.rolling(window=short_T).sum()
    vol_long = c.rolling(window=long_T).sum()

    condu = ((((ma_short > ma_long).astype(int).diff() == 1).astype(
        int) + (high_short >= high_long).astype(int)) == 2).astype(int)
    condd = ((((ma_short < ma_long).astype(int).diff() == 1).astype(
        int) + (low_short <= low_long).astype(int)) == 2).astype(int)

    ft = vol_short / vol_long

    s = condu - condd

    return s


###########################################################################################
'''章鱼哥的因子信号函数'''


def ma34_cross_sig_price(d):
    df = d.copy()
    df['ma34'] = df['close'].rolling(34).mean()
    df['hl2'] = (df['high'] + df['low']) / 2  # 计算 (high + low) / 2
    # 计算信号
    sigg = pd.Series(np.where(
        (df['hl2'] > df['ma34']) & (
            df['hl2'].shift(1) < df['ma34'].shift(1)), 1,
        np.where(
            (df['hl2'] < df['ma34']) & (
                df['hl2'].shift(1) > df['ma34'].shift(1)), -1, 0
        )
    ))
    sigg.index = df.index
    return sigg


def alpha_004_sig_price(d):
    df = d.copy()

    # 预计算滚动均值和标准差，避免重复计算
    ma8 = df['close'].rolling(8).mean()
    std8 = df['close'].rolling(8).std()
    ma2 = df['close'].rolling(2).mean()
    vol_ratio = df['volume'] / df['volume'].rolling(20).mean()

    # 计算因子
    df['alpha_004'] = np.where(
        (ma8 + std8) < ma2, -1,
        np.where(
            ma2 < (ma8 - std8), 1,
            np.where(vol_ratio >= 1, 1, -1)
        )
    )

    df['long_ma'] = df['close'].rolling(30).mean()
    df['short_ma'] = df['close'].rolling(5).mean()
    df['ma_ratio'] = df['long_ma'] / df['short_ma']

    df['alpha_004'] = np.where(
        # df['ma_ratio'] > 1.002,  # matio大于1偏做多，matio小于1偏做空
        # df['ma_ratio'] < 0.996,
        1,
        df['alpha_004'], 0
    )

    print('=' * 50)
    print(df['alpha_004'].value_counts())
    print('=' * 50)

    return df['alpha_004']


def chao2_sig_price(d):
    df = d.copy()
    # 计算rsi
    df['close_diff'] = df['close'].diff()
    # 计算 u 和 d
    df['u'] = np.where(df['close_diff'] > 0, df['close_diff'], 0)
    df['d'] = np.where(df['close_diff'] < 0, -df['close_diff'], 0)
    df['close_diff_percent'] = df['close_diff'] / df['close']
    length = 14
    # 计算
    df['smma_u'] = smma(df['u'], length)
    df['smma_d'] = smma(df['d'], length)
    # 计算 RSI14
    df['rsi'] = np.where(df['smma_d'] == 0, 100,
                         np.where(df['smma_u'] == 0, 0,
                                  100 * (df['smma_u'] / (df['smma_u'] + df['smma_d']))))
    df['rsi_diff'] = df['rsi'].diff()
    df['volume_roc'] = (
        df['volume']-df['volume'].shift(1))/df['volume'].shift(1)

    df['tr1'] = df['high'] - df['low']
    df['tr2'] = abs(df['high'] - df['close'].shift(1))  # 参考前一周期的收盘价
    df['tr3'] = abs(df['close'].shift(1) - df['low'])
    df['tr'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)

    window = 14

    # 计算 atr
    df['atr'] = smma(df['tr'], window)

    df['chao2'] = chao2_factor(df)

    df['long_ma'] = df['close'].rolling(30).mean()
    df['short_ma'] = df['close'].rolling(5).mean()
    df['ma_ratio'] = df['long_ma'] / df['short_ma']

    df['chao2'] = np.where(
        # df['ma_ratio'] > 1.004,  # matio大于1偏做多，matio小于1偏做空
        # df['ma_ratio'] < 0.991,
        1,
        df['chao2'], 0
    )

    print('='*50)
    print(df['chao2'].value_counts())
    print('='*50)

    return df['chao2']


def chao4_sig_price(d):
    df = d.copy()
    df['ma1'] = smma(df['close'], 20)
    df['ma2'] = smma(df['close'], 50)
    df['ma3'] = smma(df['close'], 100)

    df['chao4'] = np.where(
        (df['ma1'] > df['ma2']) & (df['ma2'] > df['ma3']) &
        (df['close'] < df['ma2']) & (df['close'] > df['ma3']),
        1,
        np.where(
            (df['ma1'] < df['ma2']) & (df['ma2'] < df['ma3']) &
            (df['close'] > df['ma2']) & (df['close'] < df['ma3']),
            -1,
            0
        )
    )

    df['long_ma'] = df['close'].rolling(30).mean()
    df['short_ma'] = df['close'].rolling(5).mean()
    df['ma_ratio'] = df['long_ma'] / df['short_ma']

    df['chao4'] = np.where(
        # df['ma_ratio'] > 1.002,  # matio大于1偏做多，matio小于1偏做空
        # df['ma_ratio'] < 0.993,
        1,
        df['chao4'], 0
    )

    print('='*50)
    print(df['chao4'].value_counts())
    print('='*50)

    return df['chao4']


def rsi_deviation_sig_price(d):
    df = d.copy()
    df['close_diff'] = df['close'].diff()
    # 计算 u 和 d
    df['u'] = np.where(df['close_diff'] > 0, df['close_diff'], 0)
    df['d'] = np.where(df['close_diff'] < 0, -df['close_diff'], 0)
    length = 14
    # 计算 u 和 d 的平滑移动平均值
    df['smma_u'] = smma(df['u'], length)
    df['smma_d'] = smma(df['d'], length)
    # 计算长短期均线比值
    df['long_ma'] = df['close'].rolling(30).mean()
    df['short_ma'] = df['close'].rolling(5).mean()
    df['ma_ratio'] = df['long_ma'] / df['short_ma']
    # 计算 RSI14
    df['rsi'] = np.where(df['smma_d'] == 0, 100,
                         np.where(df['smma_u'] == 0, 0,
                                  100 * (df['smma_u'] / (df['smma_u'] + df['smma_d']))))

    # 计算 atr
    df['tr1'] = df['high'] - df['low']
    df['tr2'] = abs(df['high'] - df['close'].shift(1))  # 参考前一周期的收盘价
    df['tr3'] = abs(df['close'].shift(1) - df['low'])
    df['tr'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
    window = 14
    df['atr'] = smma(df['tr'], window)
    df['price_range '] = emma(df['high'] - df['low'], 20)
    df['fluctuations'] = df['price_range ']/df['atr']

    # rsi背离因子
    df['rsi_deviation'] = rsi_deviation(df)

    return df['rsi_deviation']


###########################################################################################
'''章鱼哥的filter'''


def F_historical_volatility(d):
    '''衡量当前波动率高低的过滤器'''
    df = d.copy()
    df['HV'] = np.log(
        (df['close']/df['close'].shift(1)).rolling(20).std())
    return df['HV']


def F_volume_deviation(d):
    '''衡量当前成交量高低的过滤器'''
    df = d.copy()
    df['volume_deviation'] = (
        df['volume']-df['volume'].rolling(20).mean())/df['volume'].rolling(20).mean()
    return df['volume_deviation']


def F_price_position(d):
    '''衡量当前相对位置高低的过滤器'''
    df = d.copy()
    df['middle'] = df['close'].rolling(20).mean()
    df['upper'] = df['middle']+2*df['close'].rolling(20).std()
    df['lower'] = df['middle']-2*df['close'].rolling(20).std()
    df['price_position'] = (df['close']-df['lower'])/(df['upper']-df['lower'])
    return df['price_position']


def F_price_fluctuation(d):
    '''衡量短期价格波动快慢的过滤器'''
    df = d.copy()
    df['price_fluctuation'] = df['close'].rolling(20).std()
    return df['price_fluctuation']


def F_HV(d):
    df = d.copy()
    df['HV'] = np.log(
        (df['close']/df['close'].shift(1)).rolling(20).std())
    return df['HV']


def F_fluctuations(d):
    df = d.copy()
    df['tr1'] = df['high'] - df['low']
    df['tr2'] = abs(df['high'] - df['close'].shift(1))  # 参考前一周期的收盘价
    df['tr3'] = abs(df['close'].shift(1) - df['low'])
    df['tr'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)

    window = 14

    # 计算 atr
    df['atr'] = smma(df['tr'], window)

    df['price_range '] = emma(df['high'] - df['low'], 20)

    df['fluctuations'] = df['price_range ']/df['atr']

    s = df['fluctuations']

    # 按照分位数划分数据
    quantile_labels = pd.qcut(s.fillna(0), q=[0, 0.2, 0.4, 0.6, 0.8, 1], labels=[
        "0-20%", "20-40%", "40-60%", "60-80%", "80-100%"])

    # 确保按照标签的顺序输出
    sorted_labels = ["0-20%", "20-40%", "40-60%", "60-80%", "80-100%"]

    print('='*50)
    # 遍历每个分位区间，输出区间和均值
    for label in sorted_labels:
        segment = s[quantile_labels == label]
        print(f'{label} 的数值范围: [{segment.min()}, {segment.max()}]')
        print(f'{label} 的均值: {segment.mean()}')
        print('='*50)

    # exit()

    return df['fluctuations']


def F_combination(d):
    df = d.copy()
    long_T = 30
    short_T = 5
    ma_short = df['close'].rolling(window=short_T).mean()
    ma_long = df['close'].rolling(window=long_T).mean()

    s = ma_short / ma_long

    df['tr1'] = df['high'] - df['low']
    df['tr2'] = abs(df['high'] - df['close'].shift(1))  # 参考前一周期的收盘价
    df['tr3'] = abs(df['close'].shift(1) - df['low'])
    df['tr'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)

    window = 14

    # 计算 atr
    df['atr'] = smma(df['tr'], window)

    df['price_range '] = emma(df['high'] - df['low'], 14)

    df['fluctuations'] = df['price_range ']/df['atr']

    df['combination'] = s * df['fluctuations']

    print('='*50)
    print(df['combination'].tail(10))
    print('='*50)

    return df['combination']


###########################################################################################


def F_bandportion(d):
    c = d.close
    long_T = 30
    short_T = 5
    ma_short = c.rolling(window=short_T).mean()
    ma_long = c.rolling(window=long_T).mean()

    high_short = c.rolling(window=short_T).max()
    high_long = c.rolling(window=long_T).max()
    low_short = c.rolling(window=short_T).min()
    low_long = c.rolling(window=long_T).min()

    vol_short = d.volume.rolling(window=short_T).sum()
    vol_long = d.volume.rolling(window=long_T).sum()

    s = ma_short / ma_long

    # 按照分位数划分数据
    quantile_labels = pd.qcut(s.fillna(0), q=[0, 0.2, 0.4, 0.6, 0.8, 1], labels=[
        "0-20%", "20-40%", "40-60%", "60-80%", "80-100%"])

    # 确保按照标签的顺序输出
    sorted_labels = ["0-20%", "20-40%", "40-60%", "60-80%", "80-100%"]

    print('='*50)
    # 遍历每个分位区间，输出区间和均值
    for label in sorted_labels:
        segment = s[quantile_labels == label]
        print(f'{label} 的数值范围: [{segment.min()}, {segment.max()}]')
        print(f'{label} 的均值: {segment.mean()}')
        print('='*50)

    # exit()

    return s


###########################################################################################
'''成都的强因子'''


class BollingerBand:
    def __init__(self, c, p=20, k=2):
        self.c = c
        self.p = p
        self.k = k
        self.ma = None
        self.upper_band = None
        self.lower_band = None
        self.calculate_bollinger_bands()

    def calculate_bollinger_bands(self):
        self.ma = self.c.rolling(window=self.p).mean()  # 中轨
        rolling_std = self.c.rolling(window=self.p).std()  # 滚动标准差
        self.upper_band = self.ma + (rolling_std * self.k)  # 上轨
        self.lower_band = self.ma - (rolling_std * self.k)  # 下轨

    def get_bollinger_bands(self):
        """返回布林带计算结果"""
        return {
            'ma': self.ma,
            'upper_band': self.upper_band,
            'lower_band': self.lower_band
        }


def bollinger_breakout_downward(d):
    c = d.close
    h = d.high
    l = d.low
    new_bollinger = BollingerBand(c)
    bollinger = new_bollinger.get_bollinger_bands()
    rolling_mean = bollinger["ma"]
    lower_band = bollinger["lower_band"]

    long_T = 100
    short_T = 20
    ma_long = c.rolling(window=long_T).mean()
    ma_short = c.rolling(window=short_T, min_periods=15).mean()

    ma = ma_short / ma_long
    ma_min = ma.expanding().quantile(0.2)

    # 计算最高价最低价突破布林带
    lower_bt = l < lower_band

    # 判断连续最高价最低价突破布林带
    window_size = 4
    lower_ct = lower_bt.rolling(window=window_size).apply(
        lambda x: all(x), raw=True)

    # open_type 0不开仓,1为开多,-1为开空
    open_type = 0
    open_list = []
    for idx in range(len(d)):
        if idx >= 3:
            if lower_ct.iloc[idx]:
                open_type = 1

        if open_type == 1 and (h.iloc[idx] > rolling_mean.iloc[idx]).any() and ma.iloc[idx] < ma_min.iloc[idx]:
            open_list.append(open_type)
            open_type = 0
        else:
            open_list.append(0)

    s = pd.Series(open_list)
    s.index = d.index
    return s


def bollinger_breakout_upward(d):
    c = d.close
    h = d.high
    l = d.low
    new_bollinger = BollingerBand(c)
    bollinger = new_bollinger.get_bollinger_bands()
    rolling_mean = bollinger["ma"]
    upper_band = bollinger["upper_band"]

    long_T = 100
    short_T = 20
    ma_long = c.rolling(window=long_T).mean()
    ma_short = c.rolling(window=short_T, min_periods=15).mean()

    ma = ma_short / ma_long
    ma_max = ma.expanding().quantile(0.8)

    # 计算最高价最低价突破布林带
    upper_bt = h > upper_band

    # 判断连续最高价最低价突破布林带
    window_size = 4
    upper_ct = upper_bt.rolling(window=window_size).apply(
        lambda x: all(x), raw=True)

    # open_type 0不开仓,1为开多,-1为开空
    open_type = 0
    open_list = []
    for idx in range(len(d)):
        if idx >= 3:
            if upper_ct.iloc[idx]:
                open_type = 1

        if open_type == 1 and (l.iloc[idx] < rolling_mean.iloc[idx]).any() and ma.iloc[idx] > ma_max.iloc[idx]:
            open_list.append(open_type)
            open_type = 0
        else:
            open_list.append(0)
    s = pd.Series(open_list)
    s.index = d.index
    return s

###########################################################################################


if __name__ == '__main__':
    coin_name = 'BTCUSDT_15m_2022_2025'
    d = Data(coin_name)
    dm = DataManager()
    dm.concat_data(d)
    ea = EventAnal()
    td = ea.get_data(d)
    el, ft = ea.find_ticker_event(
        d, ma34_cross_sig_price, F_bandportion, 0, True)
    qc = pd.qcut(
        ft.fillna(0), [0, 0.2, 0.4, 0.6, 0.8, 1], labels=False)
    rs = el.T.groupby(qc).mean().T
    # rs_mean = el.T.mean().T

    ea.plot_event(d, ma34_cross_sig_price)
    rs.plot()
    el.mean(axis=1).plot(color='black')

    plt.title(coin_name)
    plt.show()
    print('done')

###########################################################################################

    # # 创建包含两个子图的图表

    # # 把rs的图和el.mean(axis=1).plot(color='black')的图显示在plotly上
    # fig = go.Figure()
    # for i in range(5):
    #     fig.add_trace(go.Scatter(x=rs.index, y=rs[i], mode='lines', name=f'{i}'))
    # # fig.add_trace(go.Scatter(x=rs_mean.index, y=rs_mean, mode='lines', name='mean', line=dict(color='black')))
    # fig.add_trace(go.Scatter(x=el.mean(axis=1).index, y=el.mean(axis=1), mode='lines', name='mean', line=dict(color='black')))
    # fig.update_layout(
    #     title='Event Signal',
    #     xaxis_title='Time',
    #     yaxis_title='Price',
    #     dragmode='zoom',
    #     xaxis=dict(rangeslider=dict(visible=True)),
    #     yaxis=dict(autorange=True),  # 添加这一行
    #     hovermode='x unified')
    # fig.show()
    # print('done')

    # # 把ea.plot_event(d, ret_ma_cross_sig_price)的图显示在plotly上
    # fig = go.Figure()
    # # td = d.data[-5000:]
    # s = ret_ma_cross_sig_price(td)
    # # fig.add_trace(go.Scatter(x=td.index, y=td.close, mode='lines', name='close'))
    # s1 = td.close.reset_index(drop=True)
    # s2 = td.close.where((s == 1), np.nan).reset_index(drop=True)
    # s3 = td.close.where((s == -1), np.nan).reset_index(drop=True)
    # fig.add_trace(
    #         go.Candlestick(
    #         x=s1.index,
    #         open=td['open'],
    #         high=td['high'],
    #         low=td['low'],
    #         close=td['close'],
    #         name='K线图'
    #     )
    # )
    # # fig.add_trace(go.Scatter(x=s1.index, y=s1, mode='lines', name='close'))
    # fig.add_trace(go.Scatter(x=s1.index, y=s2, mode='markers', name='buy', marker=dict(color='red', symbol='triangle-up', size=10)))
    # fig.add_trace(go.Scatter(x=s1.index, y=s3, mode='markers', name='sell', marker=dict(color='blue', symbol='triangle-down', size=10)))
    # fig.update_layout(
    #     title='Event Signal',
    #     xaxis_title='Time',
    #     yaxis_title='Price',
    #     dragmode='zoom',
    #     xaxis=dict(rangeslider=dict(visible=True)),
    #     yaxis=dict(autorange=True)  # 添加这一行
    #     )
    # fig.show()
    # print('done')
