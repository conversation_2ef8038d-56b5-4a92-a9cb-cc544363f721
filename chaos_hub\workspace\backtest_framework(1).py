import os
import pandas as pd
import pickle
import numpy as np
import plotly.graph_objects as go
import pandas_ta as pta
from sqlalchemy import create_engine
from plotly.subplots import make_subplots  # 新增导入


MYSQL_CONFIG = {
    "host": "**************:33306",
    "user": "root",
    "password": "1234",
    "database": "ctadata"
}

class DataManager():
    def __init__(self):
        # self.__tim__ = ticker.TickerInfoManager()
        self.problem_ticker = []
        self.engine = create_engine(f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}/{MYSQL_CONFIG['database']}")

        self.data_dir = 'data/'  # 存储数据的目录
        return

    def concat_data(self, data):
        # 定义持久化文件名
        data_file = os.path.join(self.data_dir, f"{data.contract}.pkl")
        print(f"数据读取：{data.contract}")

        # 如果存在本地文件，则读取
        if os.path.exists(data_file):
            with open(data_file, 'rb') as f:
                df = pickle.load(f)
                df = df.dropna(subset=['close'])
            print("从本地加载数据。")

            data.data = df
        else:
            """读取并合并单品类永续合约的K线数据"""
            query = f"SELECT * FROM {data.contract}"
            df = pd.read_sql(query, self.engine)
            df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
            df.set_index('open_time', inplace=True)
            df = df.dropna(subset=['close'])
            # 将所有K线数据合并
            data.data = df

            # 将获取的数据保存到本地
            with open(data_file, 'wb') as f:
                pickle.dump(df, f)
            print("从在线获取数据并保存本地。")

            return


class Data():

    def __init__(self, contract):
        self.contract = contract
        return

class EventAnal():

    def __init__(self):
        self.df = None

        return

    def get_data(self, d):
        global from_data
        global to_data

        if self.df is None:
            self.df = d.data.iloc[from_data:to_data].reset_index(drop=True)

        return self.df

    def find_ticker_event(self, d, sig, flt, sign, is_absolute=False):
        global from_data
        global to_data

        """查找事件并计算收益"""
        df = self.get_data(d)
        s = sig(df)
        fs = flt(df)
        if sign != 0:
            el = s[s == sign]
        else:
            el = s[abs(s) == 1]

        ret = {}
        fret = {}
        for i in el.index:
            eloc = df.index.get_loc(i)
            e = (df.close.iloc[-3600 + eloc:3600 + eloc] / df.close.iloc[eloc] - 1) * el[i]
            e.index = range(-3600, len(e) - 3600)
            ret[i] = e
            if is_absolute:
                fret[i] = fs.loc[i]
            else:
                fret[i] = fs.loc[i] * el[i]
        ret = pd.DataFrame(ret)
        fret = pd.Series(fret)
        return ret, fret

    def find_event(self, d, sig, flt, sign, is_absolute=False):
        ret, fret = self.find_ticker_event(d, sig, flt, sign, is_absolute)
        return ret, fret

    def plot_event(self, d, sig):
        df = d.data
        s = sig(df)
        s1 = df.close.reset_index(drop=True)
        s2 = df.close.where((s == 1), np.nan).reset_index(drop=True).plot(marker='^')
        s3 = df.close.where((s == -1), np.nan).reset_index(drop=True).plot(marker='v')
        s1.plot(color='black', alpha=0.5); s2.plot(color='red'); s3.plot(color='blue')
        return


# 假设 df 是你的 DataFrame，'balance' 是余额列
def calculate_max_drawdown(df):
    # 计算运行最大值与对应峰值索引
    running_max = df['returns'].cummax()
    # peak_indices = df['returns'].expanding().apply(lambda x: x.idxmax()).astype(int)

    # 计算回撤率
    drawdown = (df['returns'] - running_max) / running_max
    max_drawdown = drawdown.min()

    # # 定位关键节点
    # if max_drawdown < 0:
    #     trough_idx = drawdown.idxmin()  # 谷底位置
    #     peak_idx = peak_indices[trough_idx]  # 对应峰值位置
    #     duration = trough_idx - peak_idx  # 实际持续时间
    # else:
    #     duration = 0

    return max_drawdown


def calculate_returns_fully_vectorized(df, entry_signal_indices, holding_time, holding_pos, returns=None):
    if returns is None:
        returns = pd.Series(1, index=df.index, dtype=float)

    # 创建一个与returns相同长度的数组，初始值为1
    result_values = np.ones(len(returns))
    result_values[0] = returns[0]

    # 创建一个收益率变化的数组
    changes = np.zeros(len(returns))

    for entry_idx in entry_signal_indices:
        if entry_idx >= len(df.close):
            continue

        # 确定出场索引
        if 'exit_signals_index' in df.columns:
            exit_idx = df['exit_signals_index'][entry_idx]
        else:
            exit_idx = min(entry_idx + holding_time, len(df.close) - 1)

        # 计算这次交易的收益率
        trade_return = df.close[exit_idx] / df.close[entry_idx]

        # 在入场点记录收益变化
        changes[entry_idx] += (1 / holding_pos) * (trade_return - 1)

    # 累积计算收益
    for i in range(1, len(result_values)):
        # 基础收益是前一天的收益
        result_values[i] = result_values[i-1]

        # 如果当天有交易收益变化，应用它
        if changes[i] != 0:
            result_values[i] = result_values[i] * (1 + changes[i])

    # 转换为Series并返回
    return pd.Series(result_values, index=returns.index)


def exit_signals_handle(df, holding_time):
    # 创建一个空的Series来存储退出信号
    exit_signals = pd.Series(0, index=df.index)
    exit_signals_index = pd.Series(0, index=df.index)

    # 获取开仓信号的索引
    open_signal_indices = np.where(df['entry_signals'] == 1)[0]

    # 预先计算所有可能的止盈止损价格
    # stop_loss_price = df.close * 0.9
    # take_profit_price = df.close * (1 + amplitude * 5)

    for idx in open_signal_indices:
        if idx >= len(df) - 1:  # 如果是最后一个数据点，跳过
            continue

        # 计算时间条件
        time_condition = df.index >= idx + holding_time
        time_over_condition = idx >= len(df) - holding_time

        # 计算价格条件（使用向量化操作）
        # condition1 = df.close <= stop_loss_price[idx]
        # if prev_idx is None:
        #     stop_loss_condition = (df.close < previous_lower_close) & upper_mask
        # else:
        #     stop_loss_condition = (df.close < previous_lower_close) & upper_mask & idx > prev_idx + holding_time
        # filter1 = df.filter_trend < 1
        # price_condition = (condition1 | condition2) & filter1

        # 组合条件并找到第一个满足条件的点
        combined_condition = (
                time_condition
                | time_over_condition
                # | stop_loss_condition
        )

        # 只要第一个满足条件的点
        if np.any(combined_condition[idx + 1:]):
            first_exit_idx = np.argmax(combined_condition[idx + 1:]) + idx + 1
            exit_signals[first_exit_idx] = 1
            exit_signals_index[idx] = first_exit_idx

    df['exit_signals'] = exit_signals
    df['exit_signals_index'] = exit_signals_index


def intense(df):
    if 'entry_signals' in df.columns:
        return df['entry_signals']
        # return -df['exit_signals']
        # return df['entry_signals'] - df['exit_signals']

    c = df.close
    mean = c.rolling(window=3600).mean()
    deviation = (c - mean) / mean

    df['rolling_std'] = df['close'].diff().rolling(window=600).std()

    df['entry_signals'] = (
        (df['rolling_std'] > df['rolling_std'].rolling(window=3600).quantile(0.8))
        & (deviation < 0)
    ).astype(int)
    print(f"开仓点（未过滤）：{df['entry_signals'].sum()}个")

    # ================================ 风控过滤条件 =============================================
    holding_time = 60 * 60
    holding_pos = 20
    # 获取开仓信号的索引
    entry_signal_indices = np.where(df['entry_signals'] == 1)[0]
    prev_entry_price = None
    prev_entry_index = None
    for entry_signal_idx in entry_signal_indices:
        # 初始化
        if prev_entry_price is None or prev_entry_index is None:
            prev_entry_price = df.close[entry_signal_idx]
            prev_entry_index = entry_signal_idx
            continue

        # # 60根k线开单一次
        # if entry_signal_indices[open_i_i] >= entry_index + 60:
        #     entry_index = entry_signal_indices[open_i_i]
        #     break

        # AS加仓模式
        if entry_signal_idx >= holding_time:
            entry_count = df['entry_signals'][entry_signal_idx - holding_time:entry_signal_idx].sum()
        else:
            entry_count = df['entry_signals'][0:entry_signal_idx].sum()
        if (
                entry_signal_idx < prev_entry_index + holding_time
                and df.close[entry_signal_idx] > prev_entry_price * (1 - 0.001 * entry_count)
        ):
            df.loc[entry_signal_idx, 'entry_signals'] = 0
            continue

        prev_entry_index = entry_signal_idx
        prev_entry_price = df.close[entry_signal_idx]
    print(f"开仓点（风控过滤）：{df['entry_signals'].sum()}")

    # holding_time时间内只能允许持仓holding_pos份
    df.loc[df['entry_signals'].rolling(window=holding_time).sum() >= holding_pos, 'entry_signals'] = 0
    print(f"开仓点（单位时间持仓量过滤）：{df['entry_signals'].sum()}")

    # 生成平仓点
    exit_signals_handle(df, holding_time)

    # 生成收益率
    entry_signal_indices = np.where(df['entry_signals'] == 1)[0]
    global returns
    returns = calculate_returns_fully_vectorized(df, entry_signal_indices, holding_time, holding_pos)
    # returns = df['deviation_min']

    return df['entry_signals']
    # return -df['exit_signals']
    # return df['entry_signals'] - df['exit_signals']


def filter(df):
    s = df.close.rolling(20) / df.close.rolling(300)

    s_min = s.min()
    s_max = s.max()

    print(f"s 的范围是 [{s_min}, {s_max}]")

    return s


from_data = -3000000
to_data = None
returns = None
if __name__ == '__main__':
    # 0是显示过滤器，1是显示行情图（含收益曲线）

    # show = [True, False]
    show = [False, True]
    # show = [True, True]
    # show = [False, False]

    d_arr = [
        # Data('ETH_USDT_1s_2025_03_early_part'),
        # Data('POPCAT_USDT_1s_2025_03_early_part'),
        # Data('JUP_USDT_1s_2025_03_early_part'),
        # Data('SUI_USDT_1s_2025_03_early_part'),
        # Data('1000PEPE_USDT_1s_2025_03_early_part'),
        # Data('FARTCOIN_USDT_1s_2025_03_early_part'),
        # Data('DOGE_USDT_1s_2025_03_early_part'),
        # Data('SOL_USDT_1s_2025_03_early_part'),
        # Data('XRP_USDT_1s_2025_03_early_part'),

        # Data('BTC_USDT_1s_2020_03_middle_part'),
        # Data('ETH_USDT_1s_2020_03_middle_part'),
        # Data('XRP_USDT_1s_2020_03_middle_part'),
        # Data('LTC_USDT_1s_2020_03_middle_part'),
        # Data('BCH_USDT_1s_2020_03_middle_part'),

        # Data('DOGE_USDT_1s_2025_02_last_part'),
        # Data('TRUMP_USDT_1s_2025_02_last_part'),
        # Data('KAITO_USDT_1s_2025_02_last_part'),
        # Data('PNUT_USDT_1s_2025_02_last_part'),
        # Data('ENA_USDT_1s_2025_02_last_part'),
        # Data('ONDO_USDT_1s_2025_02_last_part'),
        # Data('WLD_USDT_1s_2025_02_last_part'),
        # Data('OP_USDT_1s_2025_02_last_part'),
        # Data('ADA_USDT_1s_2025_02_last_part'),
        #
        # Data('NEIRO_USDT_1s_2025_02_last_part'),
        # Data('IP_USDT_1s_2025_02_last_part'),
        # Data('LTC_USDT_1s_2025_02_last_part'),
        # Data('KOMA_USDT_1s_2025_02_last_part'),
        # Data('RUNE_USDT_1s_2025_02_last_part'),
        # Data('COOKIE_USDT_1s_2025_02_last_part'),
        # Data('LAYER_USDT_1s_2025_02_last_part'),
        # Data('SHELL_USDT_1s_2025_02_last_part'),
        # Data('XRP_USDT_1s_2025_02_last_part'),
        #
        # Data('ETH_USDT_1s_2025_02_middle_part'),
        # Data('JUP_USDT_1s_2025_02_middle_part'),
        # Data('SUI_USDT_1s_2025_02_middle_part'),
        # Data('1000PEPE_USDT_1s_2025_02_middle_part'),
        # Data('DOGE_USDT_1s_2025_02_middle_part'),
        # Data('SOL_USDT_1s_2025_02_middle_part'),
        # Data('XRP_USDT_1s_2025_02_middle_part'),
        # Data('BCH_USDT_1s_2025_02_middle_part'),
        # Data('XLM_USDT_1s_2025_02_middle_part'),
        #
        # # 测试集6，这10天是大跌行情
        # Data('ETH_USDT_1s_2024_12_middle_part'),
        # Data('JUP_USDT_1s_2024_12_middle_part'),
        # Data('SUI_USDT_1s_2024_12_middle_part'),
        # Data('1000PEPE_USDT_1s_2024_12_middle_part'),
        # Data('DOGE_USDT_1s_2024_12_middle_part'),
        # Data('SOL_USDT_1s_2024_12_middle_part'),
        # Data('XRP_USDT_1s_2024_12_middle_part'),
        # Data('BCH_USDT_1s_2024_12_middle_part'),
        # Data('XLM_USDT_1s_2024_12_middle_part'),
        #
        # # 测试集5
        # Data('ETH_USDT_1s_2024_12_last_part'),
        # Data('POPCAT_USDT_1s_2024_12_last_part'),
        # Data('JUP_USDT_1s_2024_12_last_part'),
        # Data('SUI_USDT_1s_2024_12_last_part'),
        # Data('1000PEPE_USDT_1s_2024_12_last_part'),
        # Data('FARTCOIN_USDT_1s_2024_12_last_part'),
        # Data('DOGE_USDT_1s_2024_12_last_part'),
        # Data('SOL_USDT_1s_2024_12_last_part'),
        # Data('XRP_USDT_1s_2024_12_last_part'),
        #
        # # 测试集4，这10天是大跌行情
        # Data('ETH_USDT_1s_2025_01_early_part'),
        # Data('POPCAT_USDT_1s_2025_01_early_part'),
        # Data('JUP_USDT_1s_2025_01_early_part'),
        # Data('SUI_USDT_1s_2025_01_early_part'),
        # Data('1000PEPE_USDT_1s_2025_01_early_part'),
        # Data('FARTCOIN_USDT_1s_2025_01_early_part'),
        # Data('DOGE_USDT_1s_2025_01_early_part'),
        # Data('SOL_USDT_1s_2025_01_early_part'),
        # Data('XRP_USDT_1s_2025_01_early_part'),
        #
        # # 测试集3
        # Data('ETH_USDT_1s_2025_01_middle_part'),
        # Data('POPCAT_USDT_1s_2025_01_middle_part'),
        # Data('JUP_USDT_1s_2025_01_middle_part'),
        # Data('SUI_USDT_1s_2025_01_middle_part'),
        # Data('1000PEPE_USDT_1s_2025_01_middle_part'),
        # Data('FARTCOIN_USDT_1s_2025_01_middle_part'),
        # Data('DOGE_USDT_1s_2025_01_middle_part'),
        # Data('SOL_USDT_1s_2025_01_middle_part'),
        # Data('XRP_USDT_1s_2025_01_middle_part'),
        #
        # # 测试集2
        # Data('ETH_USDT_1s_2025_01_last_part'),
        # Data('POPCAT_USDT_1s_2025_01_last_part'),
        # Data('JUP_USDT_1s_2025_01_last_part'),
        # Data('SUI_USDT_1s_2025_01_last_part'),
        # Data('1000PEPE_USDT_1s_2025_01_last_part'),
        # Data('FARTCOIN_USDT_1s_2025_01_last_part'),
        # Data('DOGE_USDT_1s_2025_01_last_part'),
        # Data('SOL_USDT_1s_2025_01_last_part'),
        # Data('XRP_USDT_1s_2025_01_last_part'),
        #
        # # 测试集1
        # Data('ETH_USDT_1s_2025_02_early_part'),
        # Data('POPCAT_USDT_1s_2025_02_early_part'),
        # Data('JUP_USDT_1s_2025_02_early_part'),
        # Data('SUI_USDT_1s_2025_02_early_part'),
        # Data('1000PEPE_USDT_1s_2025_02_early_part'),
        # Data('FARTCOIN_USDT_1s_2025_02_early_part'),
        # Data('DOGE_USDT_1s_2025_02_early_part'),
        # Data('SOL_USDT_1s_2025_02_early_part'),
        # Data('XRP_USDT_1s_2025_02_early_part'),

        # 分钟测试集
        Data('SOLUSDT_1m_2020_2024'),
        # Data('QTUMUSDT_1m_2020_2024'),
        # Data('DOGEUSDT_1m_2020_2024'),
        # Data('XRPUSDT_1m_2020_2024'),
        # Data('BTCUSDT_1m_2020_2024'),
    ]

    rst_infos = []
    sharpe_np = np.array([])
    last_np = np.array([])
    for d in d_arr:
        dm = DataManager()
        dm.concat_data(d)
        print("数据总条数：", len(d.data))
        print("列：", d.data.columns)

        ea = EventAnal()
        df = ea.get_data(d)
        el, ft = ea.find_ticker_event(d, intense, filter, 0, True)
        qc, bins = pd.qcut(ft.fillna(0), [0, 0.2, 0.4, 0.6, 0.8, 1],
                           labels=False, retbins=True, duplicates='drop')
        print("分割点：", bins)

        s = intense(df)
        # ========================== 统计部分 ===============================
        # 计算经过的总时间（以毫秒计）
        total_duration_ms = df.close_time[len(df) - 1] - df.close_time[0]
        # 将毫秒转换为秒
        total_duration_seconds = total_duration_ms / 1000
        # 使用 divmod 计算天、小时和分钟
        days, remainder = divmod(total_duration_seconds, 86400)  # 86400s in one day
        hours, remainder = divmod(remainder, 3600)  # 3600s in one hour
        minutes = remainder / 60
        # 使用条件判断统计 1 的个数
        count_1 = np.sum(s == 1)
        count_minus_1 = np.sum(s == -1)
        ts = f'{d.contract}, 经历了{int(days)}天{int(hours)}小时{int(minutes)}分钟，共开单：{count_1+count_minus_1}，多单: {count_1}，空单：{count_minus_1}。'
        df['returns'] = returns
        # 回测的实际天数
        actual_backtest_days = days

        # 计算总的数据点数量
        total_data_points = len(df['returns'])
        # 计算平均每天的数据点数
        points_per_day = total_data_points / actual_backtest_days

        annualization_factor = np.sqrt(365 * points_per_day)

        df['second_returns'] = df['returns'].pct_change().dropna()
        sharpe = (df['second_returns'].mean() / df['second_returns'].std()) * annualization_factor
        # 计算最大回撤和最大回撤持续时间
        max_drawdown = calculate_max_drawdown(df)
        rst_infos.append(f"{d.contract}, 夏普比率: {sharpe:.4f}"
                         f", 最高值：{df['returns'].max()}, 最低值：{df['returns'].min()}, 末端值：{df['returns'].iloc[-1]}"
                         f", 最大回撤: {max_drawdown}")
        sharpe_np = np.append(sharpe_np, sharpe)
        last_np = np.append(last_np, df['returns'].iloc[-1])
        # ========================== 统计部分 ===============================

        if show[0]:
            rs = el.T.groupby(qc).mean().T
            rs_mean = el.T.mean().T

            # # 创建包含两个子图的图表

            # 把rs的图和el.mean(axis=1).plot(color='black')的图显示在plotly上
            fig = go.Figure()
            for i in range(len(bins) - 1):
                fig.add_trace(go.Scatter(x=rs.index, y=rs[i], mode='lines', name=f'{i}'))
            fig.add_trace(go.Scatter(x=rs_mean.index, y=rs_mean, mode='lines', name='mean', line=dict(color='black')))
            # fig.add_trace(go.Scatter(x=el.mean(axis=1).index, y=el.mean(axis=1), mode='lines', name='mean', line=dict(color='black')))
            fig.update_layout(
                title='Event Signal',
                annotations=[
                    dict(
                        text=ts + f'{bins}',
                        xref="paper", yref="paper",
                        x=0.5, y=1.15,  # 位置在标题上方
                        showarrow=False,
                        font=dict(size=12)
                    )
                ],
                xaxis_title='Time',
                yaxis_title='Price',
                dragmode='zoom',
                xaxis=dict(rangeslider=dict(visible=True)),
                yaxis=dict(autorange=True),  # 添加这一行
                hovermode='x unified')
            fig.show()
            print('done')

        # ------------------------------------- 交易分析 ---------------------------------------

        if show[1]:
            fig = make_subplots(rows=2, cols=1,
                                shared_xaxes=True,
                                vertical_spacing=0.05,
                                row_heights=[0.7, 0.3])
            # df['fs'] = filter(df)


            s1 = df.close.reset_index(drop=True)
            s2 = df.close.where((s == 1), np.nan).reset_index(drop=True)
            s3 = df.close.where((s == -1), np.nan).reset_index(drop=True)

            # 添加行情线图到主图（row=1）
            fig.add_trace(go.Scatter(
                x=df.index,
                y=df['close'],
                name='价格',
                mode='lines',
                line={'simplify': True},  # 启用简化算法
            ), row=1, col=1)

            # 添加21日均线到主图（row=1）
            fig.add_trace(go.Scatter(
                x=df.index,
                y=df['close'].rolling(21).mean(),
                name='成本价',
                mode='lines',
                line={'color': 'orange', 'simplify': True},  # 自定义线条样式
            ), row=1, col=1)

            # 1. 买入信号
            buy_mask = ~np.isnan(s2)
            fig.add_trace(go.Scattergl(
                x=s1.index[buy_mask],
                y=s2[buy_mask],
                mode='markers',
                name='buy',
                marker=dict(
                    color='purple',
                    symbol='triangle-up',
                    size=10
                )
            ), row=1, col=1)

            # 2. 卖出信号
            sell_mask = ~np.isnan(s3)
            fig.add_trace(go.Scattergl(
                x=s1.index[sell_mask],
                y=s3[sell_mask],
                mode='markers',
                name='sell',
                marker=dict(
                    color='red',
                    symbol='triangle-down',
                    size=10
                )
            ), row=1, col=1)
            # 添加收益图
            fig.add_trace(go.Scatter(x=df.index, y=df['returns']), row=2, col=1)

            # 统一布局设置
            fig.update_layout(
                title='交易分析',
                annotations=[
                    dict(
                        text=ts,
                        xref="paper", yref="paper",
                        x=0.5, y=1.15,  # 位置在标题上方
                        showarrow=False,
                        font=dict(size=12)
                    )
                ],
                xaxis_title='时间',
                dragmode='zoom',
                hovermode='x unified',
                xaxis=dict(),
                yaxis=dict(
                    title='价格',
                    autorange=True,
                    fixedrange=False,  # 允许Y轴拖动
                    showspikes=True  # 显示悬停时的辅助线
                ),
                height=800  # 增加总图高度
            )
            fig.show()
            print('done')


    print('\n')
    for rst_info in rst_infos:
        print(rst_info)

    print(sharpe_np)
    print(last_np)
    print(f"平均夏普：{sharpe_np.mean()}, 夏普的中位数：{np.median(sharpe_np)}")
    print(f"平均末端值：{last_np.mean()}, 末端值的中位数：{np.median(last_np)}")
