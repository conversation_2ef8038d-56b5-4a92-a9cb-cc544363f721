# 📈 量化交易策略文档

> 基于订单流不平衡度和布林带指标的多交易所套利策略

---

## 📊 数据计算模块

### 1. 当前市场深度数据
- **买盘挂单量** (`cur_buy`): 计算各交易所当前买盘前10档总挂单量
- **卖盘挂单量** (`cur_sell`): 计算各交易所当前卖盘前10档总挂单量

### 2. 中间价计算
- **交易所中间价**: `(买一价 + 卖一价) / 2`
- **全市场平均中间价** (`cur_mid_price`): 所有交易所中间价的平均值

### 3. 历史数据对比
- **历史买盘挂单量** (`past_buy`): 10秒前各交易所买盘前10档总挂单量
- **历史卖盘挂单量** (`past_sell`): 10秒前各交易所卖盘前10档总挂单量

---

## 🔢 核心指标计算

### 订单不平衡度指标
| 指标 | 计算公式 | 说明 |
|------|----------|------|
| **当前订单不平衡度** | `cur_order = cur_buy - cur_sell` | 反映当前市场买卖力量对比 |
| **历史订单不平衡度** | `past_order = past_buy - past_sell` | 反映历史时点买卖力量对比 |
| **深度比率** | `depth_ratio = cur_order / past_order` | 衡量订单不平衡度的变化趋势 |

---

## 📈 布林带构建

### 🔵 当前订单不平衡度布林带 (`cur_order_boll`)
- **上轨**: `cur_order均值 + 2 × cur_order标准差`
- **中轨**: `cur_order均值`
- **下轨**: `cur_order均值 - 2 × cur_order标准差`

### 🟡 历史订单不平衡度布林带 (`past_order_boll`)
- **上轨**: `past_order均值 + 2 × past_order标准差`
- **中轨**: `past_order均值`
- **下轨**: `past_order均值 - 2 × past_order标准差`

### 🟢 深度比率布林带 (`depth_ratio_boll`)
- **上轨**: `depth_ratio均值 + 1 × depth_ratio标准差`
- **中轨**: `depth_ratio均值`
- **下轨**: `depth_ratio均值 - 1 × depth_ratio标准差`

---

## 🎯 交易信号策略

### 📈 做多信号
**触发条件**:
- ✅ `cur_order_boll` 上轨 < 0
- ✅ `past_order_boll` 上轨 < 0
- ✅ `depth_ratio_boll` 下轨 < 0

**执行操作**: 以 `cur_mid_price` 价格下限价买单

### 📉 做空信号
**触发条件**:
- ✅ `cur_order_boll` 下轨 < 0
- ✅ `past_order_boll` 下轨 < 0
- ✅ `depth_ratio_boll` 下轨 < 0

**执行操作**: 以 `cur_mid_price` 价格下限价卖单

---

## 🔄 平仓策略

### 🔴 平多信号
**触发条件**:
- 当前触发做空信号
- `当前持有空单数量 - 未成交空单数量 ≥ 0.002`

**执行操作**: 平多仓 0.002 份额

### 🟢 平空信号
**触发条件**:
- 当前触发做多信号
- `当前持有多单数量 - 未成交多单数量 ≥ 0.002`

**执行操作**: 平空仓 0.002 份额

---

## ⚙️ 参数配置

| 参数名称 | 数值 | 说明 |
|----------|------|------|
| 历史数据时间窗口 | 10秒 | 用于计算历史订单不平衡度 |
| 深度档位 | 前10档 | 计算挂单量的深度范围 |
| 平仓份额 | 0.002 | 每次平仓的固定份额（可配置） |
| 布林带标准差倍数 | 2倍/1倍 | 订单不平衡度使用2倍，深度比率使用1倍 |

---

*📝 注: 平仓份额 0.002 为手动传入的可配置参数*