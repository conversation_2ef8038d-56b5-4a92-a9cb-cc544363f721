{"cells": [{"cell_type": "code", "execution_count": null, "id": "9fbc9282", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n"]}, {"cell_type": "code", "execution_count": 2, "id": "37fe28e6", "metadata": {}, "outputs": [], "source": ["# 原始文件名列表\n", "file_names = [\n", "    \"1000BONKUSDT_15m_2020_2025.txt\", \"ETHUSDT_15m_2020_2025.txt\", \"POLUSDT_15m_2020_2025.txt\",\n", "    \"1000FLOKIUSDT_15m_2020_2025.txt\", \"FARTCOINUSDT_15m_2020_2025.txt\", \"QNTUSDT_15m_2020_2025.txt\",\n", "    \"1000PEPEUSDT_15m_2020_2025.txt\", \"FETUSDT_15m_2020_2025.txt\", \"RENDERUSDT_15m_2020_2025.txt\",\n", "    \"1000SATSUSDT_15m_2020_2025.txt\", \"FILUSDT_15m_2020_2025.txt\", \"SANDUSDT_15m_2020_2025.txt\",\n", "    \"1000SHIBUSDT_15m_2020_2025.txt\", \"FORMUSDT_15m_2020_2025.txt\", \"SEIUSDT_15m_2020_2025.txt\",\n", "    \"AAVEUSDT_15m_2020_2025.txt\", \"GALAUSDT_15m_2020_2025.txt\", \"SOLUSDT_15m_2020_2025.txt\",\n", "    \"ADAUSDT_15m_2020_2025.txt\", \"GRTUSDT_15m_2020_2025.txt\", \"SPXUSDT_15m_2020_2025.txt\",\n", "    \"AEROUSDT_15m_2020_2025.txt\", \"HBARUSDT_15m_2020_2025.txt\", \"STXUSDT_15m_2020_2025.txt\",\n", "    \"ALGOUSDT_15m_2020_2025.txt\", \"HYPEUSDT_15m_2020_2025.txt\", \"SUIUSDT_15m_2020_2025.txt\",\n", "    \"APTUSDT_15m_2020_2025.txt\", \"ICPUSDT_15m_2020_2025.txt\", \"SUPERUSDT_15m_2020_2025.txt\",\n", "    \"ARBUSDT_15m_2020_2025.txt\", \"IMXUSDT_15m_2020_2025.txt\", \"SUSDT_15m_2020_2025.txt\",\n", "    \"ATOMUSDT_15m_2020_2025.txt\", \"INJUSDT_15m_2020_2025.txt\", \"TAOUSDT_15m_2020_2025.txt\",\n", "    \"AUSDT_15m_2020_2025.txt\", \"IOTAUSDT_15m_2020_2025.txt\", \"THETAUSDT_15m_2020_2025.txt\",\n", "    \"AVAXUSDT_15m_2020_2025.txt\", \"IPUSDT_15m_2020_2025.txt\", \"TIAUSDT_15m_2020_2025.txt\",\n", "    \"BCHUSDT_15m_2020_2025.txt\", \"JTOUSDT_15m_2020_2025.txt\", \"TONUSDT_15m_2020_2025.txt\",\n", "    \"BNBUSDT_15m_2020_2025.txt\", \"JUPUSDT_15m_2020_2025.txt\", \"TRUMPUSDT_15m_2020_2025.txt\",\n", "    \"BSVUSDT_15m_2020_2025.txt\", \"KAIAUSDT_15m_2020_2025.txt\", \"TRXUSDT_15m_2020_2025.txt\",\n", "    \"BTCUSDT_15m_2020_2025.txt\", \"KASUSDT_15m_2020_2025.txt\", \"UNIUSDT_15m_2020_2025.txt\",\n", "    \"CAKEUSDT_15m_2020_2025.txt\", \"LDOUSDT_15m_2020_2025.txt\", \"VETUSDT_15m_2020_2025.txt\",\n", "    \"CRVUSDT_15m_2020_2025.txt\", \"LINKUSDT_15m_2020_2025.txt\", \"VIRTUALUSDT_15m_2020_2025.txt\",\n", "    \"DEXEUSDT_15m_2020_2025.txt\", \"LTCUSDT_15m_2020_2025.txt\", \"WIFUSDT_15m_2020_2025.txt\",\n", "    \"DOGEUSDT_15m_2020_2025.txt\", \"MKRUSDT_15m_2020_2025.txt\", \"WLDUSDT_15m_2020_2025.txt\",\n", "    \"DOTUSDT_15m_2020_2025.txt\", \"NEARUSDT_15m_2020_2025.txt\", \"XLMUSDT_15m_2020_2025.txt\",\n", "    \"ENAUSDT_15m_2020_2025.txt\", \"ONDOUSDT_15m_2020_2025.txt\", \"XMRUSDT_15m_2020_2025.txt\",\n", "    \"ENSUSDT_15m_2020_2025.txt\", \"OPUSDT_15m_2020_2025.txt\", \"XRPUSDT_15m_2020_2025.txt\",\n", "    \"ETCUSDT_15m_2020_2025.txt\", \"PAXGUSDT_15m_2020_2025.txt\", \"ZECUSDT_15m_2020_2025.txt\"\n", "]\n", "\n", "# 去除.txt后缀\n", "cleaned_names = [name.replace('.txt', '') for name in file_names]"]}, {"cell_type": "code", "execution_count": 4, "id": "83dd499d", "metadata": {}, "outputs": [{"data": {"text/plain": ["78"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["len(cleaned_names)"]}, {"cell_type": "code", "execution_count": 5, "id": "76c2b4f7", "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame({'Symbol': cleaned_names})\n", "df.to_excel('crypto_symbols.xlsx', index=False)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}