"""
测试单均线阈值策略
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 创建测试数据
def create_test_data(num_bars=1000):
    """
    创建测试数据
    """
    start_date = datetime(2023, 1, 1)
    dates = [start_date + timedelta(minutes=15*i) for i in range(num_bars)]
    
    # 生成模拟价格数据
    np.random.seed(42)  # 设置随机种子以获得可重复的结果
    
    # 基础价格趋势
    base_price = 2000
    trend = np.linspace(0, 200, num_bars)  # 上升趋势
    noise = np.random.normal(0, 20, num_bars)  # 随机噪声
    
    close_prices = base_price + trend + noise
    
    # 生成OHLC数据
    data = []
    for i, (date, close) in enumerate(zip(dates, close_prices)):
        # 生成合理的OHLC数据
        volatility = 10
        high = close + np.random.uniform(0, volatility)
        low = close - np.random.uniform(0, volatility)
        
        if i == 0:
            open_price = close
        else:
            open_price = close_prices[i-1] + np.random.uniform(-5, 5)
        
        volume = np.random.uniform(1000, 10000)
        
        data.append({
            'datetime': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('datetime', inplace=True)
    return df

# 测试策略
def test_strategy():
    """
    测试策略
    """
    print("创建测试数据...")
    df = create_test_data(1000)
    
    print("导入策略模块...")
    try:
        from vnpy_backtester.scripts.run_single_ma_threshold_strategy import run_single_ma_threshold_strategy_backtest
        
        print("运行策略回测...")
        engine = run_single_ma_threshold_strategy_backtest(
            df=df,
            long_window=5,          # 减少均线窗口以更容易触发信号
            threshold=0.0,          # 阈值
            position_size=1000,     # 每次开仓金额（按金额下单）
            range_window=20,        # 计算价格范围的窗口（减少以适应测试数据）
            profit_close_volume_1=300,   # 第一次利润平仓的金额
            profit_close_volume_2=400,   # 第二次利润平仓的金额
            profit_multiplier_1=0.5,  # 降低利润平仓倍数以更容易触发
            profit_multiplier_2=1.0,  # 降低利润平仓倍数以更容易触发
            time_close_bars=50,     # 时间平仓的K线数量（减少以适应测试）
            time_close_volume=300,  # 时间平仓的金额
            rate=0.0003,
            slippage=0.01,
            capital=100000,         # 增加初始资金
            order_type="amount",    # 按金额下单
            plot_show=False,        # 测试时不显示图表
            plot_save=False,        # 测试时不保存图表
            debug=True,             # 开启调试模式
        )
        
        print("策略测试完成！")
        return True
        
    except Exception as e:
        print(f"策略测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_strategy()
    if success:
        print("✅ 策略测试成功！")
    else:
        print("❌ 策略测试失败！")
