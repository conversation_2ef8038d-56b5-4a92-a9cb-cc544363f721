{"cells": [{"cell_type": "code", "execution_count": 7, "id": "aa7e4cbc", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from factor_evaluation.factor_evaluation import FactorEvaluation\n", "from factor_evaluation.factor_evaluation import DataService"]}, {"cell_type": "code", "execution_count": 1, "id": "9fb6268c", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from factor_evaluation_server import FactorEvaluation, DataService"]}, {"cell_type": "code", "execution_count": 8, "id": "06666049", "metadata": {}, "outputs": [], "source": ["ds=DataService()\n", "df=ds['ETHUSDT_15m_2020_2025']['2021-10-01':]"]}, {"cell_type": "code", "execution_count": null, "id": "6965b85d", "metadata": {}, "outputs": [], "source": ["evaluator=FactorEvaluation(df=df,future_return_periods=10)"]}, {"cell_type": "code", "execution_count": null, "id": "5ff067ef", "metadata": {}, "outputs": [], "source": ["def ma_deviation_factor(df,ma_period=20):\n", "    ma=df['close'].rolling(ma_period).mean()\n", "    return (df['close']-ma)/ma"]}, {"cell_type": "code", "execution_count": null, "id": "0658975c", "metadata": {}, "outputs": [], "source": ["evaluator.set_factor(\n", "    factor_data_or_func=ma_deviation_factor,\n", "    factor_name='ma_deviation_factor'\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5bbe7543", "metadata": {}, "outputs": [], "source": ["result=evaluator.run_full_evaluation()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}